# SSH 密钥认证配置指南

为了避免每次部署都输入密码，建议配置SSH密钥认证。

## 1. 生成SSH密钥对（如果还没有）

```bash
# 生成新的SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 按提示操作：
# - 选择密钥保存位置（默认：~/.ssh/id_rsa）
# - 设置密码短语（可选，建议设置）
```

## 2. 将公钥复制到服务器

### 方法一：使用 ssh-copy-id（推荐）
```bash
ssh-copy-id -p 22 root@***************
```

### 方法二：手动复制
```bash
# 1. 显示公钥内容
cat ~/.ssh/id_rsa.pub

# 2. 登录服务器
ssh root@***************

# 3. 在服务器上创建 .ssh 目录（如果不存在）
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# 4. 将公钥内容添加到 authorized_keys 文件
echo "你的公钥内容" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# 5. 退出服务器
exit
```

## 3. 测试密钥认证

```bash
# 测试SSH密钥连接
ssh -p 22 root@*************** "echo 'SSH密钥认证成功'"
```

如果成功，应该不需要输入密码就能连接。

## 4. 配置SSH客户端（可选）

创建或编辑 `~/.ssh/config` 文件：

```bash
Host myserver
    HostName ***************
    User root
    Port 22
    IdentityFile ~/.ssh/id_rsa
```

然后可以使用简化命令：
```bash
ssh myserver
```

## 5. 安全建议

1. **设置密钥密码短语** - 为私钥设置密码保护
2. **禁用密码认证** - 在服务器上禁用SSH密码登录（可选）
3. **定期更换密钥** - 建议定期更换SSH密钥
4. **备份私钥** - 安全备份您的私钥文件

## 故障排除

### 常见问题

1. **权限问题**
   ```bash
   # 修复权限
   chmod 700 ~/.ssh
   chmod 600 ~/.ssh/id_rsa
   chmod 644 ~/.ssh/id_rsa.pub
   chmod 600 ~/.ssh/authorized_keys  # 在服务器上
   ```

2. **SELinux问题**（CentOS/RHEL）
   ```bash
   # 在服务器上执行
   restorecon -R ~/.ssh
   ```

3. **SSH服务配置**
   检查服务器 `/etc/ssh/sshd_config` 文件：
   ```
   PubkeyAuthentication yes
   AuthorizedKeysFile .ssh/authorized_keys
   ```

配置完成后，部署脚本将自动使用密钥认证，无需输入密码。
