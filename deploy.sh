#!/bin/bash

# 构建和部署脚本
# 使用方法: ./deploy.sh [服务器地址] [用户名]
# 例如: ./deploy.sh example.com root

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    log_error "使用方法: $0 <服务器地址> <用户名> [端口号]"
    log_error "例如: $0 example.com root"
    log_error "例如: $0 example.com root 22"
    exit 1
fi

SERVER=$1
USER=$2
PORT=${3:-22}  # 默认端口22
REMOTE_PATH="/home/<USER>"
LOCAL_DIST_PATH="./dist"
BACKUP_PATH="/home/<USER>"

log_info "开始部署到服务器: $USER@$SERVER:$PORT"
log_info "远程路径: $REMOTE_PATH"

# 检查本地依赖
log_info "检查本地环境..."

if ! command -v pnpm &> /dev/null; then
    log_error "pnpm 未安装，请先安装 pnpm"
    exit 1
fi

if ! command -v rsync &> /dev/null; then
    log_error "rsync 未安装，请先安装 rsync"
    exit 1
fi

if ! command -v ssh &> /dev/null; then
    log_error "ssh 未安装，请先安装 ssh"
    exit 1
fi

# 检查是否能连接到服务器
log_info "测试服务器连接..."
if ! ssh -p $PORT -o ConnectTimeout=10 -o BatchMode=yes $USER@$SERVER exit 2>/dev/null; then
    log_error "无法连接到服务器 $USER@$SERVER:$PORT"
    log_error "请检查："
    log_error "1. 服务器地址和端口是否正确"
    log_error "2. SSH密钥是否已配置"
    log_error "3. 网络连接是否正常"
    exit 1
fi
log_success "服务器连接测试成功"

# 清理旧的构建产物
log_info "清理旧的构建产物..."
if [ -d "$LOCAL_DIST_PATH" ]; then
    rm -rf "$LOCAL_DIST_PATH"
    log_success "已清理旧的构建产物"
fi

# 构建项目
log_info "开始构建项目..."
log_info "执行命令: pnpm build-without-type-check"

if ! pnpm build-without-type-check; then
    log_error "构建失败"
    exit 1
fi

# 检查构建产物
if [ ! -d "$LOCAL_DIST_PATH" ]; then
    log_error "构建产物目录不存在: $LOCAL_DIST_PATH"
    exit 1
fi

if [ -z "$(ls -A $LOCAL_DIST_PATH)" ]; then
    log_error "构建产物目录为空: $LOCAL_DIST_PATH"
    exit 1
fi

log_success "项目构建完成"
log_info "构建产物位置: $LOCAL_DIST_PATH"

# 显示构建产物信息
log_info "构建产物内容:"
ls -la "$LOCAL_DIST_PATH"

# 备份远程服务器上的旧文件
log_info "备份远程服务器上的旧文件..."
ssh -p $PORT $USER@$SERVER "
    if [ -d '$REMOTE_PATH' ]; then
        echo '备份旧文件到: $BACKUP_PATH'
        cp -r '$REMOTE_PATH' '$BACKUP_PATH'
        echo '备份完成'
    else
        echo '远程目录不存在，将创建新目录'
        mkdir -p '$REMOTE_PATH'
    fi
"

# 上传新文件
log_info "上传构建产物到服务器..."
log_info "使用 rsync 同步文件..."

# 使用rsync同步，删除远程多余文件，保持同步
if rsync -avz --delete -e "ssh -p $PORT" "$LOCAL_DIST_PATH/" "$USER@$SERVER:$REMOTE_PATH/"; then
    log_success "文件上传完成"
else
    log_error "文件上传失败"
    
    # 如果上传失败，尝试恢复备份
    log_warning "尝试恢复备份..."
    ssh -p $PORT $USER@$SERVER "
        if [ -d '$BACKUP_PATH' ]; then
            rm -rf '$REMOTE_PATH'
            mv '$BACKUP_PATH' '$REMOTE_PATH'
            echo '已恢复备份'
        fi
    "
    exit 1
fi

# 验证部署
log_info "验证部署结果..."
ssh -p $PORT $USER@$SERVER "
    echo '远程目录内容:'
    ls -la '$REMOTE_PATH'
    echo ''
    echo '磁盘使用情况:'
    du -sh '$REMOTE_PATH'
"

# 清理备份（可选，保留最近3个备份）
log_info "清理旧备份..."
ssh -p $PORT $USER@$SERVER "
    cd /home
    # 保留最近3个备份，删除更旧的
    ls -t www_backup_* 2>/dev/null | tail -n +4 | xargs -r rm -rf
    echo '备份清理完成'
"

log_success "部署完成！"
log_info "网站已更新到: $USER@$SERVER:$REMOTE_PATH"
log_info "备份位置: $BACKUP_PATH"

# 可选：显示部署后的URL提示
log_info "如果您的网站配置正确，现在可以访问更新后的网站了"
