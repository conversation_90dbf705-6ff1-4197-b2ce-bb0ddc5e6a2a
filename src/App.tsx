import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import "./App.css";

// 布局组件
import Layout from "./components/Layout";
import ProtectedRoute from "./components/ProtectedRoute";
import PermissionGuard from "./components/PermissionGuard";
import AuthProvider from "./components/AuthProvider";

// 页面组件
import Dashboard from "./pages/Dashboard";
import Project from "./pages/Project";
import ProjectDetail from "./pages/Project/Detail";
import ProjectCreate from "./pages/Project/Create";
import Task from "./pages/Task";
import TaskDetail from "./pages/Task/Detail";
import TaskCreate from "./pages/Task/Create";
import User from "./pages/User";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import FeishuCallback from "./pages/FeishuCallback";
import AIModelPage from "./pages/AIModel";
import AIPromptPage from "./pages/AIPrompt";
import { useAuthStore } from "./store/authStore";

// 开发环境工具（仅在开发环境加载）
if (process.env.NODE_ENV === "development") {
  import("./utils/devUtils");
}

function App() {
  const { isAuthenticated } = useAuthStore();

  return (
    <AuthProvider>
      <BrowserRouter>
        <Routes>
          <Route
            path="/"
            element={
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} />
            }
          />
          <Route path="/login" element={<Login />} />
          <Route path="/feishu-callback" element={<FeishuCallback />} />

          {/* 受保护的路由 */}
          <Route element={<ProtectedRoute />}>
            <Route element={<Layout />}>
              {/* 工作台 - 所有登录用户都可以访问 */}
              <Route path="dashboard" element={<Dashboard />} />

              {/* 项目管理 - 需要项目查看权限 */}
              <Route path="project">
                <Route
                  index
                  element={
                    <PermissionGuard requiredPermissions={["view_project"]}>
                      <Project />
                    </PermissionGuard>
                  }
                />
                <Route
                  path=":id"
                  element={
                    <PermissionGuard requiredPermissions={["view_project"]}>
                      <ProjectDetail />
                    </PermissionGuard>
                  }
                />
                <Route
                  path="create"
                  element={
                    <PermissionGuard requiredPermissions={["create-project"]}>
                      <ProjectCreate />
                    </PermissionGuard>
                  }
                />
              </Route>

              {/* 任务管理 - 至少需要查看自己任务的权限 */}
              <Route path="task">
                <Route
                  index
                  element={
                    <PermissionGuard requiredPermissions={["view_task"]}>
                      <Task />
                    </PermissionGuard>
                  }
                />
                <Route
                  path=":id"
                  element={
                    <PermissionGuard requiredPermissions={["view_task"]}>
                      <TaskDetail />
                    </PermissionGuard>
                  }
                />
                <Route
                  path="create"
                  element={
                    <PermissionGuard requiredPermissions={["create_task"]}>
                      <TaskCreate />
                    </PermissionGuard>
                  }
                />
              </Route>

              {/* 用户管理 - 需要用户管理权限 */}
              <Route
                path="user"
                element={
                  <PermissionGuard requiredPermissions={["manage_users"]}>
                    <User />
                  </PermissionGuard>
                }
              />

              {/* 系统设置 - 需要系统设置权限 */}
              <Route
                path="settings"
                element={
                  <PermissionGuard requiredPermissions={["system_settings"]}>
                    <Settings />
                  </PermissionGuard>
                }
              />

              {/* AI模型管理 - 需要系统设置权限 */}
              <Route
                path="ai-model"
                element={
                  <PermissionGuard requiredPermissions={["manager_model"]}>
                    <AIModelPage />
                  </PermissionGuard>
                }
              />

              {/* 提示词管理 - 需要系统设置权限 */}
              <Route
                path="ai-prompt"
                element={
                  <PermissionGuard requiredPermissions={["manager_prompt"]}>
                    <AIPromptPage />
                  </PermissionGuard>
                }
              />
            </Route>
          </Route>

          {/* 404页面 */}
          <Route
            path="*"
            element={
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} />
            }
          />
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  );
}

export default App;
