import { create } from 'zustand'
import type { UserInfo } from '@/store/authStore'
import { formatTasks, type Task } from '@/store/taskStore'
import httpClient from '@/utils/httpClient'
import { formatDateTime } from '@/utils/datetime'

// 项目状态定义
export const PROJECT_STATUS = {
  PROCESSING: { value: 0, label: '进行中' },
  PENDING: { value: 1, label: '待开始' },
  COMPLETED: { value: 2, label: '已完成' },
  PAUSED: { value: 3, label: '暂停' },
  CANCELED: { value: 4, label: '已取消' }
}

// 项目优先级定义
export const PROJECT_PRIORITY = {
  HIGH: { value: 0, label: '高' },
  MEDIUM: { value: 1, label: '中' },
  LOW: { value: 2, label: '低' }
}

// 根据值获取状态对象
export const getStatusByValue = (value: number | string) => {
  const numValue = typeof value === 'string' ? parseInt(value, 10) : value
  return Object.values(PROJECT_STATUS).find(status => status.value === numValue) || PROJECT_STATUS.PROCESSING
}

// 根据值获取优先级对象
export const getPriorityByValue = (value: number | string) => {
  const numValue = typeof value === 'string' ? parseInt(value, 10) : value
  return Object.values(PROJECT_PRIORITY).find(priority => priority.value === numValue) || PROJECT_PRIORITY.MEDIUM
}

// 项目状态类型
export type ProjectStatus = typeof PROJECT_STATUS[keyof typeof PROJECT_STATUS]['label']

// 项目优先级类型
export type ProjectPriority = typeof PROJECT_PRIORITY[keyof typeof PROJECT_PRIORITY]['label']

// 项目团队成员接口
export interface ProjectMember {
  id: string
  name: string
  role: string
  avatar: string
}

// 项目接口
export interface Project {
  id: string
  projectId: string
  projectName: string
  description: string
  status: ProjectStatus
  priority: ProjectPriority
  creator: string
  creatorName: string
  createdTime: string
  deadline: string
  taskCount: number
  reviewerCount: number
  completedTasks: number
  totalIssueCount: number
  completeIssueCount: number
  totalPages: number
  reviewerAvatars: string[]
  teamMembers: string[]
  progress: number
  tasks: Task[]
  createUser: UserInfo
  reviewers: UserInfo[]
}

export interface DashboardInfo {
  projectCount: number
  taskCount: number
  completeIssueCount: number
  totalIssueCount: number
  projectList: Project[]
  taskList: Task[]  
}

// 项目筛选条件
interface ProjectFilters {
  searchTerm: string
  statusFilter: string
  priorityFilter: string
}

// 项目列表状态
interface ProjectListState {
  // 数据
  dashboardInfo: DashboardInfo | null
  projects: Project[]
  filters: ProjectFilters
  loading: boolean
  error: string | null
  currentProject: Project | null
  
  // 方法
  fetchDashboardInfo: () => Promise<void>
  fetchProjects: () => Promise<void>
  fetchProjectDetail: (id: string) => Promise<Project | null>
  setFilters: (filters: Partial<ProjectFilters>) => void
  getProjectById: (id: string) => Project | undefined
}

export const useProjectStore = create<ProjectListState>((set, get) => ({
  projects: [],
  filters: {
    searchTerm: "",
    statusFilter: "all",
    priorityFilter: "all",
  },
  loading: false,
  error: null,
  currentProject: null,
  dashboardInfo: null,

  // 获取仪表盘信息
  fetchDashboardInfo: async () => {
    try {
      set({ loading: true, error: null })
      
      // 调用API获取仪表盘信息
      const data = await httpClient.get('project/dashboard')
      const taskList = data.taskList.map(formatTasks)
      set({ dashboardInfo: { ...data, taskList } })
    } catch (error) {
      console.error('获取仪表盘信息失败:', error)
      set({ error: error instanceof Error ? error.message : '获取仪表盘信息失败' })
    } finally {
      set({ loading: false })
    }
  },
  
  // 获取项目列表
  fetchProjects: async () => {
    try {
      set({ loading: true, error: null })
      
      // 调用API获取项目列表
      const data = await httpClient.get('project/list')
      
      // 如果API返回成功，处理并保存数据
      if (Array.isArray(data)) {
        // 处理API返回的数据格式，转换为前端使用的格式
        const processedProjects = data.map((project: any) => {
          // 计算项目进度
          const totalIssueCount = project.totalIssueCount || 0
          const completeIssueCount = project.completeIssueCount || 0
          const progress = Math.round(totalIssueCount > 0 ? (completeIssueCount / totalIssueCount) * 100 : 0)
          
          // 转换优先级格式 (0=高, 1=中, 2=低)
          const priority = getPriorityByValue(project.priority)
          
          // 转换状态格式
          const status = getStatusByValue(project.status)
          
          // 提取团队成员
          const teamMembers = project.tasks 
            ? [...new Set(project.tasks.map((task: any) => task.reviewer))]
            : []
          
          // 计算总页数 (如果API没有提供，则使用默认值)
          const totalPages = project.totalPages || 0
          
          return {
            projectId: project.projectId || '',
            projectName: project.projectName || '',
            description: project.description || '',
            status: status.label,
            priority: priority.label,
            creator: project.creator || '',
            creatorName: project.creatorName || '',
            createdTime: formatDateTime(project.createdAt),
            taskCount: project.taskCount || 0,
            reviewerCount: project.reviewerCount || 0,
            totalPages,
            totalIssueCount,
            completeIssueCount,
            reviewerAvatars: project.reviewerAvatars || [],
            teamMembers,
            progress,
            deadline: formatDateTime(project.deadline),
            tasks: project.tasks ? project.tasks.map(formatTasks) : []
          } as Project
        })
        
        set({ projects: processedProjects })
      } else {
        // 如果API返回格式不正确，抛出错误
        throw new Error('API返回数据格式不正确')
      }
    } catch (error) {
      console.error('获取项目列表失败:', error)
      set({ error: error instanceof Error ? error.message : '获取项目列表失败' })
    } finally {
      set({ loading: false })
    }
  },
  
  // 获取项目详情
  fetchProjectDetail: async (id: string) => {
    try {
      set({ loading: true, error: null })
      
      // 调用API获取项目详情
      const project = await httpClient.get('project/detail', { params: { id } })
      
      // 如果API返回成功，处理并保存数据
      if (project) {
        // 计算项目进度
        const totalIssueCount = project.totalIssueCount || 0
        const completeIssueCount = project.completeIssueCount || 0
        const progress = Math.round(totalIssueCount > 0 ? (completeIssueCount / totalIssueCount) * 100 : 0)
        
        // 转换优先级格式
        const priority = getPriorityByValue(project.priority)
        
        // 转换状态格式
        const status = getStatusByValue(project.status)
        
        // 提取团队成员
        const teamMembers = project.tasks 
          ? [...new Set(project.tasks.map((task: any) => task.reviewer))]
          : []
        
        // 处理项目数据
        const processedProject = {
          id: project.id || '',
          projectId: project.projectId || '',
          projectName: project.projectName || '',
          description: project.description || '',
          status: status.label,
          priority: priority.label,
          creator: project.creator || '',
          creatorName: project.creatorName || '',
          createdTime: formatDateTime(project.createdTime),
          deadline: formatDateTime(project.deadline),
          reviewerCount: project.reviewerCount || 0,
          totalIssueCount,
          completeIssueCount,
          totalPages: project.totalPages || 0,
          teamMembers,
          progress,
          tasks: project.tasks ? project.tasks.map(formatTasks) : [],
          createUser: project.createUser || {},
          reviewers: project.reviewers || []
        } as Project
        
        set({ currentProject: processedProject })
        return processedProject
      } else {
        // 如果API返回格式不正确，抛出错误
        throw new Error('API返回数据格式不正确')
      }
    } catch (error) {
      console.error('获取项目详情失败:', error)
      set({ error: error instanceof Error ? error.message : '获取项目详情失败' })
      return null
    } finally {
      set({ loading: false })
    }
  },
  
  // 设置筛选条件
  setFilters: (filters) => {
    set({ filters: { ...get().filters, ...filters } })
  },
  
  // 根据ID获取项目
  getProjectById: (id: string) => {
    return get().projects.find(project => project.projectId === id)
  }
})) 