import { create } from 'zustand'
import httpClient from '@/utils/httpClient'
import { formatDateTime } from '@/utils/datetime'

// 格式化任务数据中的时间字段
export const formatTasks = (task: Task): Task => {
  const { totalIssueCount, completeIssueCount, status, priority, deadline, createdTime, updatedTime } = task
  const progress = Math.round(totalIssueCount > 0 ? (completeIssueCount / totalIssueCount) * 100 : 0)
  const fileType = task.fileName?.split('.').pop() || ''
  return {
    ...task,
    statusObject: getTaskStatusByValue(status) || TASK_STATUS.PROCESSING,
    priorityObject: getTaskPriorityByValue(priority) || TASK_PRIORITY.MEDIUM,
    progress,
    deadline: formatDateTime(deadline),
    createdTime: formatDateTime(createdTime),
    updatedTime: formatDateTime(updatedTime),
    fileType,
  }
}

// 任务状态定义
const TASK_STATUS = {
  PROCESSING: { value: 0, label: 'AI审核中', color: 'bg-blue-100 text-blue-800' },
  TODO: { value: 1, label: '待人工复核', color: 'bg-gray-100 text-gray-800' },
  COMPLETED: { value: 2, label: '已完成', color: 'bg-yellow-100 text-yellow-800' },
  ISSUE: { value: 3, label: '未知', color: 'bg-red-100 text-red-800' }
}

// 任务优先级定义
const TASK_PRIORITY = {
  HIGH: { value: 0, label: '高', color: 'bg-red-100 text-red-800' },
  MEDIUM: { value: 1, label: '中', color: 'bg-yellow-100 text-yellow-800' },
  LOW: { value: 2, label: '低', color: 'bg-green-100 text-green-800' }
}

// 根据值获取状态对象
export const getTaskStatusByValue = (value: number | string) => {
  const numValue = typeof value === 'string' ? parseInt(value, 10) : value
  return Object.values(TASK_STATUS).find(status => status.value === numValue) || TASK_STATUS.PROCESSING
}

// 根据值获取优先级对象
export const getTaskPriorityByValue = (value: number | string) => {
  const numValue = typeof value === 'string' ? parseInt(value, 10) : value
  return Object.values(TASK_PRIORITY).find(priority => priority.value === numValue) || TASK_PRIORITY.MEDIUM
}

// 任务类型
export interface Task {
  id: string
  taskId: string
  taskName: string
  description: string
  reviewerId: string
  reviewer: string
  bookUrl: string
  checkFileUrl?: string
  status: number
  progress: number
  issues?: TaskIssue[]
  priority: number
  deadline: string
  fileName?: string
  fileType?: string
  creator?: string
  createdTime?: string
  updatedTime?: string
  projectId?: string
  projectName?: string
  content?: string
  totalIssueCount: number
  completeIssueCount: number
  statusObject: typeof TASK_STATUS[keyof typeof TASK_STATUS]
  priorityObject: typeof TASK_PRIORITY[keyof typeof TASK_PRIORITY]
}

export interface TaskIssue {
  id: string
  issueId: string
  issueName: string
  issueType: string
  originText: string
  shortSuggestion: string
  fullSuggestion: string
  position: string
  status: number
  severity: string
  promptId: string
  modelId: string
}

// 任务筛选条件
interface TaskFilters {
  searchTerm: string
  statusFilter: string
  priorityFilter: string
  projectFilter: string
}

// 任务列表状态
interface TaskListState {
  tasks: Task[]
  filters: TaskFilters
  loading: boolean
  error: string | null
  currentTask: Task | null

  fetchTasks: () => Promise<void>
  fetchTaskDetail: (id: string) => Promise<Task | null>
  setFilters: (filters: Partial<TaskFilters>) => void
  addTask: (task: Partial<Task>) => Promise<boolean>
  updateTask: (id: string, task: Partial<Task>) => Promise<boolean>
  deleteTask: (id: string) => Promise<boolean>
}

export const useTaskStore = create<TaskListState>((set, get) => ({
  tasks: [],
  filters: {
    searchTerm: '',
    statusFilter: 'all',
    priorityFilter: 'all',
    projectFilter: 'all',
  },
  loading: false,
  error: null,
  currentTask: null,

  // 获取任务列表
  fetchTasks: async () => {
    try {
      set({ loading: true, error: null })
      const data = await httpClient.get('task/list')
      if (Array.isArray(data)) {
        set({ tasks: data.map(formatTasks) })
      } else {
        throw new Error('API返回数据格式不正确')
      }
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '获取任务列表失败' })
    } finally {
      set({ loading: false })
    }
  },

  // 获取任务详情
  fetchTaskDetail: async (id: string) => {
    try {
      set({ loading: true, error: null })
      const task = await httpClient.get('task/detail', { params: { id } })
      if (task) {
        const formattedTask = formatTasks(task)
        set({ currentTask: formattedTask })
        return formattedTask
      } else {
        throw new Error('API返回数据格式不正确')
      }
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '获取任务详情失败' })
      return null
    } finally {
      set({ loading: false })
    }
  },

  // 设置筛选条件
  setFilters: (filters) => {
    set({ filters: { ...get().filters, ...filters } })
  },

  // 新增任务
  addTask: async (task) => {
    try {
      set({ loading: true, error: null })
      await httpClient.post('task/saveTask', task)
      await get().fetchTasks()
      return true
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '添加任务失败' })
      return false
    } finally {
      set({ loading: false })
    }
  },

  // 更新任务
  updateTask: async (id, task) => {
    try {
      set({ loading: true, error: null })
      await httpClient.put(`task/updateTask/${id}`, task)
      await get().fetchTasks()
      return true
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '更新任务失败' })
      return false
    } finally {
      set({ loading: false })
    }
  },

  // 删除任务
  deleteTask: async (id) => {
    try {
      set({ loading: true, error: null })
      await httpClient.delete(`task/deleteTask/${id}`)
      set({ tasks: get().tasks.filter((task: Task) => task.id !== id) })
      return true
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '删除任务失败' })
      return false
    } finally {
      set({ loading: false })
    }
  },
}))
