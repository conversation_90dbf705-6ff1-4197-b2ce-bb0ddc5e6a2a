import { create } from "zustand";
import httpClient from "@/utils/httpClient";

// 用户角色类型
export type UserRole = "ROLE_ADMIN" | "ROLE_LEADER" | "ROLE_MEMBER";
// 用户状态类型
export type UserStatus = "active" | "inactive" | "pending";

// 用户权限类型
export type Permission =
  | "manage_users"
  | "view_users"
  | "create-project"
  | "create_task"
  | "assign_task"
  | "submitAIAudit"
  | "review_documents"
  | "view_project"
  | "view_project_detail"
  | "view_task_detail"
  | "view_task"
  | "view_prompt"
  | "view_model"
  | "manager_prompt"
  | "manager_model"
  | "update_issue"
  | "system_settings"
  | "manage_team";

// 用户信息接口
export interface User {
  id?: string;
  userId?: string;
  name: string;
  email: string;
  roles?: UserRole[];
  status: UserStatus;
  lastLogin: string;
  taskCount?: number;
  avatar: string;
  permissions?: Permission[];
}

// 角色定义
export interface Role {
  value: string;
  label: string;
  description: string;
  permissions: Permission[];
}

// 筛选条件
interface UserFilters {
  searchTerm: string;
  roleFilter: string;
  statusFilter: string;
}

// 用户列表状态
interface UserListState {
  // 数据
  users: User[];
  roles: Role[];
  filters: UserFilters;
  loading: boolean;
  error: string | null;

  // 方法
  fetchUsers: () => Promise<void>;
  setFilters: (filters: Partial<UserFilters>) => void;
  addUser: (user: Omit<User, "id">) => Promise<boolean>;
  updateUser: (id: string, userData: Partial<User>) => Promise<boolean>;
  deleteUser: (id: string) => Promise<boolean>;
  toggleUserStatus: (id: string) => Promise<boolean>;
}

export const useUserStore = create<UserListState>((set, get) => ({
  users: [],
  roles: [
    {
      value: "admin",
      label: "管理员",
      description: "拥有系统所有权限",
      permissions: [
        "manage_users",
        "create_task",
        "assign_task",
        "view_task",
        "system_settings",
        "create-project",
        "view_project",
      ],
    },
    {
      value: "manager",
      label: "组长",
      description: "拥有项目管理权限",
      permissions: [
        "manage_team",
        "create_task",
        "assign_task",
        "view_task",
        "system_settings",
        "create-project",
      ],
    },
    {
      value: "user",
      label: "审核员",
      description: "执行一般审核任务",
      permissions: [
        "review_documents",
        "view_task",
        "view_project",
        "update_issue",
      ],
    },
  ],
  filters: {
    searchTerm: "",
    roleFilter: "all",
    statusFilter: "all",
  },
  loading: false,
  error: null,

  // 获取用户列表
  fetchUsers: async () => {
    try {
      set({ loading: true, error: null });

      // 调用API获取用户列表
      const data = await httpClient.get("user/list");

      // 如果后端API正常工作，使用API返回的数据
      if (Array.isArray(data)) {
        set({ users: data });
      }
    } catch (error) {
      console.error("获取用户列表失败:", error);
      set({
        error: error instanceof Error ? error.message : "获取用户列表失败",
      });
    } finally {
      set({ loading: false });
    }
  },

  // 设置筛选条件
  setFilters: (filters) => {
    set({ filters: { ...get().filters, ...filters } });
  },

  // 添加用户
  addUser: async (userData) => {
    try {
      set({ loading: true, error: null });

      // 调用API添加用户
      await httpClient.post("users", userData);

      // 重新获取用户列表
      await get().fetchUsers();

      return true;
    } catch (error) {
      console.error("添加用户失败:", error);
      set({ error: error instanceof Error ? error.message : "添加用户失败" });
      return false;
    } finally {
      set({ loading: false });
    }
  },

  // 更新用户
  updateUser: async (id, userData) => {
    try {
      set({ loading: true, error: null });

      // 调用API更新用户
      await httpClient.put(`users/${id}`, userData);

      // 更新本地状态
      set({
        users: get().users.map((user) =>
          user.id === id ? { ...user, ...userData } : user
        ),
      });

      return true;
    } catch (error) {
      console.error("更新用户失败:", error);
      set({ error: error instanceof Error ? error.message : "更新用户失败" });
      return false;
    } finally {
      set({ loading: false });
    }
  },

  // 删除用户
  deleteUser: async (id) => {
    try {
      set({ loading: true, error: null });

      // 调用API删除用户
      await httpClient.delete(`users/${id}`);

      // 更新本地状态
      set({
        users: get().users.filter((user) => user.id !== id),
      });

      return true;
    } catch (error) {
      console.error("删除用户失败:", error);
      set({ error: error instanceof Error ? error.message : "删除用户失败" });
      return false;
    } finally {
      set({ loading: false });
    }
  },

  // 切换用户状态
  toggleUserStatus: async (id) => {
    try {
      // 获取当前用户
      const user = get().users.find((u) => u.id === id);
      if (!user) {
        throw new Error("用户不存在");
      }

      // 切换状态
      const newStatus: UserStatus =
        user.status === "active" ? "inactive" : "active";

      // 调用更新方法
      return await get().updateUser(id, { status: newStatus });
    } catch (error) {
      console.error("切换用户状态失败:", error);
      set({
        error: error instanceof Error ? error.message : "切换用户状态失败",
      });
      return false;
    }
  },
}));
