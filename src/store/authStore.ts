import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import httpClient from "@/utils/httpClient";
import type { UserRole, Permission } from "./userStore";

// 用户信息接口
export interface UserInfo {
  name: string;
  avatar: string;
  userId: string;
  email?: string;
  mobile?: string;
  roles?: UserRole[];
  permissions?: Permission[];
}

// 认证状态类型
interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  userInfo: UserInfo | null;
  loading: boolean;

  // 方法
  login: (code: string) => Promise<{ success: boolean; error?: string }>;
  loginByPassword: (
    username: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  updateUserInfo: (userInfo: UserInfo) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      loading: false,
      accessToken: null,
      userInfo: null,

      login: async (code: string) => {
        set({ loading: true });
        try {
          console.log("开始登录流程，授权码:", code);

          // 调用后端接口获取访问令牌和用户信息
          const data = await httpClient
            .post("user/login", { tempCode: code })
            .catch((error) => {
              console.error("登录请求失败:", error);
              return null;
            });

          console.log("登录API解析后数据:", data);

          if (!data) {
            return { success: false, error: "登录请求失败" };
          }

          // 检查API返回格式
          if (!data.accessToken) {
            console.error("缺少访问令牌:", data);
            return { success: false, error: "服务器返回的访问令牌不存在" };
          }

          // 检查用户信息格式
          console.log("最终使用的用户信息:", data);
          const { accessToken, ...userInfo } = data;

          set({
            isAuthenticated: true,
            accessToken,
            userInfo,
            loading: false,
          });

          console.log("登录完成，用户:", userInfo.name);
          return { success: true };
        } catch (error) {
          console.error("登录失败:", error);
          return { success: false, error: "登录过程中发生错误" };
        } finally {
          set({ loading: false });
        }
      },

      loginByPassword: async (username: string, password: string) => {
        set({ loading: true });
        try {
          const data = await httpClient.post("user/loginByPassword", {
            username,
            password,
          });

          console.log("登录API解析后数据:", data);

          if (!data) {
            return { success: false, error: "登录请求失败" };
          }

          // 检查API返回格式
          if (!data.accessToken) {
            console.error("缺少访问令牌:", data);
            return { success: false, error: "服务器返回的访问令牌不存在" };
          }

          // 检查用户信息格式
          console.log("最终使用的用户信息:", data);
          const { accessToken, ...userInfo } = data;

          // 更新状态（zustand-persist会自动将数据保存到localStorage）
          set({
            isAuthenticated: true,
            accessToken,
            userInfo,
            loading: false,
          });

          return { success: true };
        } catch (error) {
          console.error("登录失败:", error);
          return { success: false, error: "登录过程中发生错误" };
        } finally {
          set({ loading: false });
        }
      },

      logout: () => {
        // 清除状态（zustand-persist会自动更新localStorage）
        set({
          isAuthenticated: false,
          accessToken: null,
          userInfo: null,
          loading: false,
        });

        // 重定向到登录页
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      },

      updateUserInfo: (userInfo: UserInfo) => {
        set({
          userInfo,
          loading: false,
        });
      },
    }),
    {
      name: "auth-storage", // 存储在localStorage中的键名
      storage: createJSONStorage(() => localStorage),
      // 只持久化这些字段
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        accessToken: state.accessToken,
        userInfo: state.userInfo,
      }),
    }
  )
);

// 辅助函数，检查登录状态并重定向
export const checkAuthAndRedirect = (
  isProtectedRoute: boolean = true,
  redirectTo: string = "/login"
): void => {
  // 确保在客户端执行
  if (typeof window !== "undefined") {
    const { isAuthenticated } = useAuthStore.getState();

    if (isProtectedRoute && !isAuthenticated) {
      // 如果是受保护的路由且未登录，重定向到登录页
      window.location.href = redirectTo;
    } else if (!isProtectedRoute && isAuthenticated) {
      // 如果是公开路由（如登录页）且已登录，重定向到工作台
      window.location.href = "/dashboard";
    }
  }
};
