import { create } from 'zustand'
import httpClient from '@/utils/httpClient'
import { formatDateTime } from '@/utils/dateUtil'

export interface AIModel {
  modelId: string
  name: string
  orgnizer: string
  token: string
  apiurl: string
  createdTime: string
  updatedTime: string
}

export interface AIPrompt {
  promptId: string
  type: string
  text: string
  score: number
  count: number
}

interface AIModelState {
  models: AIModel[]
  prompts: AIPrompt[]
  loading: boolean
  error: string | null

  fetchModels: () => Promise<void>
  fetchPrompts: () => Promise<void>
}

export const useAIModelStore = create<AIModelState>((set, get) => ({
  models: [],
  prompts: [],
  loading: false,
  error: null,

  fetchModels: async () => {
    try {
      set({ loading: true })
      const response = await httpClient.get('/ai/model/list')
      // 格式化时间字段
      const formattedModels = response.map((model: AIModel) => ({
        ...model,
        createdTime: formatDateTime(model.createdTime),
        updatedTime: formatDateTime(model.updatedTime)
      }))
      set({ models: formattedModels, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '获取模型列表失败' })
    }
  },

  fetchPrompts: async () => {
    try {
      set({ loading: true })
      const response = await httpClient.get('/ai/prompt/list')
      set({ prompts: response, loading: false })
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '获取提示词列表失败' })
    }
  }
}))