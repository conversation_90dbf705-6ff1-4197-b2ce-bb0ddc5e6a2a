/**
 * HTTP请求工具类
 * 封装fetch API，统一处理请求和响应
 */

// 从环境变量获取API基础URL
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:8080";

// 请求配置类型
interface RequestOptions extends RequestInit {
  params?: Record<string, string | number | boolean | undefined | null>;
  timeout?: number;
  disableLog?: boolean; // 是否禁用日志
}

// 响应类型
interface ApiResponse<T = any> {
  errorCode: number;
  errorMsg: string;
  data: T;
}

/**
 * 日志工具
 */
const logger = {
  request: (method: string, url: string, data?: any, headers?: Headers) => {
    const timestamp = new Date().toISOString();
    console.group(`🚀 请求 [${timestamp}] ${method} ${url}`);
    if (headers) {
      const headersObj: Record<string, string> = {};
      headers.forEach((value, key) => {
        headersObj[key] = value;
      });
      console.log("请求头:", headersObj);
    }
    if (data) console.log("请求数据:", data);
    console.groupEnd();
  },

  response: (
    method: string,
    url: string,
    status: number,
    data?: any,
    duration?: number
  ) => {
    const timestamp = new Date().toISOString();
    const durationText = duration ? ` (${duration}ms)` : "";

    if (status >= 200 && status < 300) {
      console.group(
        `✅ 响应 [${timestamp}]${durationText} ${method} ${url} ${status}`
      );
    } else {
      console.group(
        `❌ 响应 [${timestamp}]${durationText} ${method} ${url} ${status}`
      );
    }

    if (data) console.log("响应数据:", data);
    console.groupEnd();
  },

  error: (method: string, url: string, error: any, duration?: number) => {
    const timestamp = new Date().toISOString();
    const durationText = duration ? ` (${duration}ms)` : "";

    console.group(`❌ 错误 [${timestamp}]${durationText} ${method} ${url}`);
    console.error("错误信息:", error);
    console.groupEnd();
  },
};

/**
 * 构建完整URL，包括查询参数
 */
function buildUrl(endpoint: string, params?: Record<string, any>): string {
  // 确保endpoint不以/开头
  const path = endpoint.startsWith("/") ? endpoint.slice(1) : endpoint;
  const url = new URL(`${API_BASE_URL}/${path}`);

  // 添加查询参数
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });
  }

  return url.toString();
}

/**
 * 处理HTTP请求
 */
async function request<T = any>(
  endpoint: string,
  options: RequestOptions = {}
): Promise<T> {
  const {
    params,
    timeout = 30000,
    disableLog = true,
    ...fetchOptions
  } = options;
  const method = fetchOptions.method || "GET";
  const startTime = Date.now();

  // 默认请求头
  const headers = new Headers(fetchOptions.headers);
  if (
    !headers.has("Content-Type") &&
    !(fetchOptions.body instanceof FormData)
  ) {
    headers.set("Content-Type", "application/json");
  }

  // 如果有token，添加到请求头
  const token = localStorage.getItem("auth-storage")
    ? JSON.parse(localStorage.getItem("auth-storage") || "{}")?.state
        ?.accessToken
    : null;

  if (token) {
    headers.set("Authorization", `Bearer ${token}`);
  }

  // 构建URL
  const url = buildUrl(endpoint, params);

  // 打印请求日志
  if (!disableLog) {
    logger.request(
      method,
      url,
      fetchOptions.body instanceof FormData
        ? "文件上传"
        : fetchOptions.body
        ? JSON.parse(String(fetchOptions.body))
        : params,
      headers
    );
  }

  // 处理请求超时
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    const duration = Date.now() - startTime;

    // 检查HTTP状态码
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // 打印错误日志
      if (!disableLog) {
        logger.error(method, url, errorData, duration);
      }

      throw new Error(
        errorData.message ||
          `请求失败: ${response.status} ${response.statusText}`
      );
    }

    // 如果响应是204 No Content，返回null
    if (response.status === 204) {
      // 打印响应日志
      if (!disableLog) {
        logger.response(method, url, response.status, null, duration);
      }

      return null as T;
    }

    // 解析响应JSON
    const data: ApiResponse<T> = await response.json();

    // 打印响应日志
    if (!disableLog) {
      logger.response(method, url, response.status, data, duration);
    }

    // 检查API响应状态码
    if (data.errorCode !== 10000) {
      throw new Error(data.errorMsg || "请求失败");
    }

    return data.data;
  } catch (error) {
    clearTimeout(timeoutId);
    const duration = Date.now() - startTime;

    // 打印错误日志
    if (!disableLog) {
      logger.error(method, url, error, duration);
    }

    if (error instanceof DOMException && error.name === "AbortError") {
      throw new Error("请求超时");
    }

    throw error;
  }
}

/**
 * HTTP客户端
 */
const httpClient = {
  /**
   * GET请求
   */
  get<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    return request<T>(endpoint, { ...options, method: "GET" });
  },

  /**
   * POST请求
   */
  post<T = any>(
    endpoint: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<T> {
    return request<T>(endpoint, {
      ...options,
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  /**
   * PUT请求
   */
  put<T = any>(
    endpoint: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<T> {
    return request<T>(endpoint, {
      ...options,
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  /**
   * DELETE请求
   */
  delete<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    return request<T>(endpoint, { ...options, method: "DELETE" });
  },

  /**
   * 文件上传
   */
  upload<T = any>(
    endpoint: string,
    formData: FormData,
    onProgress?: (percent: number) => void,
    disableLog: boolean = false
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      const url = buildUrl(endpoint);
      const startTime = Date.now();

      // 打印请求日志
      if (!disableLog) {
        logger.request("POST", url, "文件上传");
      }

      // 监听上传进度
      if (onProgress) {
        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            onProgress(percent);

            // 打印上传进度
            if (!disableLog && percent % 20 === 0) {
              // 每20%打印一次
              console.log(`📤 上传进度: ${percent}%`);
            }
          }
        });
      }

      // 处理请求完成
      xhr.addEventListener("load", () => {
        const duration = Date.now() - startTime;

        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);

            // 打印响应日志
            if (!disableLog) {
              logger.response("POST", url, xhr.status, response, duration);
            }

            resolve(response.data);
          } catch (error) {
            // 打印错误日志
            if (!disableLog) {
              logger.error("POST", url, "解析响应失败", duration);
            }

            reject(new Error("解析响应失败"));
          }
        } else {
          // 打印错误日志
          if (!disableLog) {
            logger.error(
              "POST",
              url,
              `上传失败: ${xhr.status} ${xhr.statusText}`,
              duration
            );
          }

          reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`));
        }
      });

      // 处理请求错误
      xhr.addEventListener("error", () => {
        const duration = Date.now() - startTime;

        // 打印错误日志
        if (!disableLog) {
          logger.error("POST", url, "网络错误", duration);
        }

        reject(new Error("网络错误"));
      });

      // 处理请求超时
      xhr.addEventListener("timeout", () => {
        const duration = Date.now() - startTime;

        // 打印错误日志
        if (!disableLog) {
          logger.error("POST", url, "请求超时", duration);
        }

        reject(new Error("请求超时"));
      });

      // 获取token
      const token = localStorage.getItem("auth-storage")
        ? JSON.parse(localStorage.getItem("auth-storage") || "{}")?.state
            ?.accessToken
        : null;

      // 发起请求
      xhr.open("POST", url);
      if (token) {
        xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      }
      xhr.send(formData);
    });
  },
};

export default httpClient;
