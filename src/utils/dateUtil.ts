// 通用时间处理工具

/**
 * 格式化日期时间
 * @param dateTime 输入的时间字符串或Date对象
 * @param format 格式模板，默认 'yyyy-MM-dd hh:mm'
 * @returns 格式化后的字符串
 */
export function formatDateTime(dateTime: string | Date, format = 'yyyy-MM-dd hh:mm'): string {
  if (!dateTime) return ''
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
  if (isNaN(date.getTime())) return ''

  const map: Record<string, string> = {
    'yyyy': date.getFullYear().toString(),
    'MM': String(date.getMonth() + 1).padStart(2, '0'),
    'dd': String(date.getDate()).padStart(2, '0'),
    'hh': String(date.getHours()).padStart(2, '0'),
    'mm': String(date.getMinutes()).padStart(2, '0'),
    'ss': String(date.getSeconds()).padStart(2, '0'),
  }

  let result = format
  Object.entries(map).forEach(([k, v]) => {
    result = result.replace(k, v)
  })
  return result
} 