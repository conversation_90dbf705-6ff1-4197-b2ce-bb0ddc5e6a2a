import type { UserInfo } from "@/store/authStore";
import type { UserRole, Permission } from "@/store/userStore";
import { ROLE_PERMISSIONS } from "./permissions";

/**
 * 开发环境工具函数
 * 用于模拟不同角色的用户进行权限测试
 */

// 模拟用户数据
export const MOCK_USERS: Record<UserRole, UserInfo> = {
  ROLE_ADMIN: {
    name: "管理员",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=admin",
    userId: "admin-001",
    email: "<EMAIL>",
    mobile: "13800138001",
    roles: ["ROLE_ADMIN"],
    permissions: ROLE_PERMISSIONS.ROLE_ADMIN,
  },
  ROLE_LEADER: {
    name: "组长",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=manager",
    userId: "manager-001",
    email: "<EMAIL>",
    mobile: "13800138002",
    roles: ["ROLE_LEADER"],
    permissions: ROLE_PERMISSIONS.ROLE_LEADER,
  },
  ROLE_MEMBER: {
    name: "组员",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=user",
    userId: "user-001",
    email: "<EMAIL>",
    mobile: "13800138003",
    roles: ["ROLE_MEMBER"],
    permissions: ROLE_PERMISSIONS.ROLE_MEMBER,
  },
};

/**
 * 开发环境权限测试工具类
 */
export class DevPermissionTester {
  /**
   * 切换到指定角色的用户（仅开发环境）
   */
  static switchToRole(role: UserRole): void {
    if (process.env.NODE_ENV !== "development") {
      console.warn("switchToRole 只能在开发环境中使用");
      return;
    }

    const mockUser = MOCK_USERS[role];

    // 更新 localStorage 中的认证信息
    const authStorage = {
      state: {
        isAuthenticated: true,
        accessToken: `mock-token-${role}-${Date.now()}`,
        userInfo: mockUser,
      },
      version: 0,
    };

    localStorage.setItem("auth-storage", JSON.stringify(authStorage));

    // 刷新页面以应用新的用户信息
    window.location.reload();

    console.log(`已切换到角色: ${role}`, mockUser);
  }

  /**
   * 获取所有可用的测试角色
   */
  static getAvailableRoles(): UserRole[] {
    return Object.keys(MOCK_USERS) as UserRole[];
  }

  /**
   * 获取指定角色的权限列表
   */
  static getRolePermissions(role: UserRole): Permission[] {
    return ROLE_PERMISSIONS[role] || [];
  }

  /**
   * 打印当前用户的权限信息
   */
  static printCurrentUserPermissions(): void {
    const authStorage = localStorage.getItem("auth-storage");
    if (!authStorage) {
      console.log("用户未登录");
      return;
    }

    try {
      const auth = JSON.parse(authStorage);
      const userInfo = auth.state?.userInfo;

      if (!userInfo) {
        console.log("用户信息不存在");
        return;
      }

      console.group("当前用户权限信息");
      console.log("用户名:", userInfo.name);
      console.log("角色:", userInfo.role);
      console.log("权限列表:", userInfo.permissions);
      console.groupEnd();
    } catch (error) {
      console.error("解析用户信息失败:", error);
    }
  }

  /**
   * 测试权限检查功能
   */
  static testPermissionCheck(permission: Permission): void {
    const authStorage = localStorage.getItem("auth-storage");
    if (!authStorage) {
      console.log(`权限测试 [${permission}]: 用户未登录`);
      return;
    }

    try {
      const auth = JSON.parse(authStorage);
      const userPermissions = auth.state?.userInfo?.permissions || [];
      const hasPermission = userPermissions.includes(permission);

      console.log(
        `权限测试 [${permission}]: ${hasPermission ? "✅ 有权限" : "❌ 无权限"}`
      );
    } catch (error) {
      console.error("权限测试失败:", error);
    }
  }

  /**
   * 批量测试多个权限
   */
  static testMultiplePermissions(permissions: Permission[]): void {
    console.group("批量权限测试");
    permissions.forEach((permission) => {
      this.testPermissionCheck(permission);
    });
    console.groupEnd();
  }

  /**
   * 显示权限测试面板（在浏览器控制台中）
   */
  static showTestPanel(): void {
    if (process.env.NODE_ENV !== "development") {
      console.warn("权限测试面板只能在开发环境中使用");
      return;
    }

    console.group("🔐 权限测试面板");
    console.log("可用命令:");
    console.log('1. DevPermissionTester.switchToRole("admin") - 切换到管理员');
    console.log(
      '2. DevPermissionTester.switchToRole("manager") - 切换到项目经理'
    );
    console.log('3. DevPermissionTester.switchToRole("user") - 切换到审核员');
    console.log(
      "4. DevPermissionTester.printCurrentUserPermissions() - 查看当前用户权限"
    );
    console.log(
      '5. DevPermissionTester.testPermissionCheck("manage_users") - 测试特定权限'
    );
    console.log("6. DevPermissionTester.testAllPermissions() - 测试所有权限");
    console.groupEnd();
  }

  /**
   * 测试所有权限
   */
  static testAllPermissions(): void {
    const allPermissions: Permission[] = [
      "manage_users",
      "view_users",
      "create-project",
      "create_task",
      "assign_task",
      "submitAIAudit",
      "review_documents",
      "view_project",
      "view_project_detail",
      "view_task_detail",
      "view_task",
      "view_prompt",
      "view_model",
      "manager_prompt",
      "manager_model",
      "update_issue",
      "system_settings",
      "manage_team",
    ];

    this.testMultiplePermissions(allPermissions);
  }
}

// 在开发环境中将工具挂载到全局对象上，方便在控制台中使用
if (process.env.NODE_ENV === "development") {
  (window as any).DevPermissionTester = DevPermissionTester;

  // 导入RBAC测试工具
  import("./rbacTests").then(({ default: RBACTester }) => {
    (window as any).RBACTester = RBACTester;
  });

  // 自动显示测试面板
  // setTimeout(() => {
  // DevPermissionTester.showTestPanel();
  // console.log("🧪 运行 new RBACTester().runAllTests() 来测试RBAC系统");
  // }, 1000);
}

export default DevPermissionTester;
