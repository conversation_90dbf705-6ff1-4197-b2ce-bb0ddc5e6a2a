import {
  PermissionUtils,
  ROUTE_PERMISSIONS,
  ROLE_PERMISSIONS,
} from "./permissions";
import type { UserRole, Permission } from "@/store/userStore";

/**
 * RBAC系统测试工具
 * 用于验证权限系统的正确性
 */

interface TestResult {
  testName: string;
  passed: boolean;
  message: string;
}

export class RBACTester {
  private results: TestResult[] = [];

  /**
   * 运行所有测试
   */
  runAllTests(): TestResult[] {
    this.results = [];

    console.group("🧪 RBAC系统测试");

    this.testRolePermissions();
    this.testRoutePermissions();
    this.testPermissionUtils();
    this.testPermissionHierarchy();

    const passedTests = this.results.filter((r) => r.passed).length;
    const totalTests = this.results.length;

    console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);

    if (passedTests === totalTests) {
      console.log("✅ 所有测试通过！");
    } else {
      console.log("❌ 部分测试失败，请检查实现");
      this.results
        .filter((r) => !r.passed)
        .forEach((result) => {
          console.error(`❌ ${result.testName}: ${result.message}`);
        });
    }

    console.groupEnd();

    return this.results;
  }

  /**
   * 测试角色权限映射
   */
  private testRolePermissions(): void {
    console.group("测试角色权限映射");

    // 测试管理员权限
    this.addTest(
      "管理员应该拥有所有权限",
      () => {
        const adminPermissions = ROLE_PERMISSIONS.admin;
        const allPermissions: Permission[] = [
          "manage_users",
          "create-project",
          "create_task",
          "assign_task",
          "review_documents",
          "view_project",
          "view_all_tasks",
          "view_team_tasks",
          "view_own_tasks",
          "update_issue",
          "system_settings",
          "manage_team",
        ];

        return allPermissions.every((permission) =>
          adminPermissions.includes(permission)
        );
      },
      "管理员缺少某些权限"
    );

    // 测试组长权限
    this.addTest(
      "组长应该有管理权限但不能管理用户",
      () => {
        const managerPermissions = ROLE_PERMISSIONS.manager;
        const shouldHave: Permission[] = [
          "manage_team",
          "create_task",
          "assign_task",
          "system_settings",
        ];
        const shouldNotHave: Permission[] = ["manage_users"];

        const hasRequired = shouldHave.every((p) =>
          managerPermissions.includes(p)
        );
        const lacksRestricted = shouldNotHave.every(
          (p) => !managerPermissions.includes(p)
        );

        return hasRequired && lacksRestricted;
      },
      "组长权限配置不正确"
    );

    // 测试普通用户权限
    this.addTest(
      "普通用户应该只有基本权限",
      () => {
        const userPermissions = ROLE_PERMISSIONS.user;
        const shouldHave: Permission[] = [
          "review_documents",
          "view_own_tasks",
          "update_issue",
        ];
        const shouldNotHave: Permission[] = [
          "manage_users",
          "create_task",
          "system_settings",
        ];

        const hasRequired = shouldHave.every((p) =>
          userPermissions.includes(p)
        );
        const lacksRestricted = shouldNotHave.every(
          (p) => !userPermissions.includes(p)
        );

        return hasRequired && lacksRestricted;
      },
      "普通用户权限配置不正确"
    );

    console.groupEnd();
  }

  /**
   * 测试路由权限配置
   */
  private testRoutePermissions(): void {
    console.group("测试路由权限配置");

    this.addTest(
      "用户管理路由应该需要manage_users权限",
      () => {
        const userRoutePermissions = ROUTE_PERMISSIONS["/user"];
        return (
          userRoutePermissions && userRoutePermissions.includes("manage_users")
        );
      },
      "用户管理路由权限配置错误"
    );

    this.addTest(
      "设置路由应该需要system_settings权限",
      () => {
        const settingsRoutePermissions = ROUTE_PERMISSIONS["/settings"];
        return (
          settingsRoutePermissions &&
          settingsRoutePermissions.includes("system_settings")
        );
      },
      "设置路由权限配置错误"
    );

    this.addTest(
      "工作台路由应该对所有用户开放",
      () => {
        const dashboardPermissions = ROUTE_PERMISSIONS["/dashboard"];
        return dashboardPermissions && dashboardPermissions.length === 0;
      },
      "工作台路由权限配置错误"
    );

    console.groupEnd();
  }

  /**
   * 测试权限工具函数
   */
  private testPermissionUtils(): void {
    console.group("测试权限工具函数");

    this.addTest(
      "hasAnyPermission应该正确检查权限",
      () => {
        const userPermissions: Permission[] = [
          "view_own_tasks",
          "review_documents",
        ];
        const requiredPermissions: Permission[] = [
          "manage_users",
          "view_own_tasks",
        ];

        return PermissionUtils.hasAnyPermission(
          userPermissions,
          requiredPermissions
        );
      },
      "hasAnyPermission函数工作不正确"
    );

    this.addTest(
      "hasAllPermissions应该正确检查所有权限",
      () => {
        const userPermissions: Permission[] = [
          "view_own_tasks",
          "review_documents",
        ];
        const requiredPermissions: Permission[] = [
          "view_own_tasks",
          "review_documents",
        ];

        return PermissionUtils.hasAllPermissions(
          userPermissions,
          requiredPermissions
        );
      },
      "hasAllPermissions函数工作不正确"
    );

    this.addTest(
      "canAccessRoute应该正确检查路由访问权限",
      () => {
        const adminPermissions = ROLE_PERMISSIONS.admin;
        const userPermissions = ROLE_PERMISSIONS.user;

        const adminCanAccessUser = PermissionUtils.canAccessRoute(
          adminPermissions,
          "/user"
        );
        const userCannotAccessUser = !PermissionUtils.canAccessRoute(
          userPermissions,
          "/user"
        );

        return adminCanAccessUser && userCannotAccessUser;
      },
      "canAccessRoute函数工作不正确"
    );

    console.groupEnd();
  }

  /**
   * 测试权限层级关系
   */
  private testPermissionHierarchy(): void {
    console.group("测试权限层级关系");

    this.addTest(
      "管理员权限应该包含组长的所有权限",
      () => {
        const adminPermissions = ROLE_PERMISSIONS.admin;
        const managerPermissions = ROLE_PERMISSIONS.manager;

        return managerPermissions.every((permission) =>
          adminPermissions.includes(permission)
        );
      },
      "管理员权限不包含组长权限"
    );

    this.addTest(
      "组长权限应该包含普通用户的部分权限",
      () => {
        const managerPermissions = ROLE_PERMISSIONS.manager;
        const userPermissions = ROLE_PERMISSIONS.user;

        // 组长应该至少有一些普通用户的权限
        const sharedPermissions = userPermissions.filter((p) =>
          managerPermissions.includes(p)
        );
        return sharedPermissions.length > 0;
      },
      "组长权限与普通用户权限没有重叠"
    );

    console.groupEnd();
  }

  /**
   * 添加测试结果
   */
  private addTest(
    testName: string,
    testFn: () => boolean,
    errorMessage: string
  ): void {
    try {
      const passed = testFn();
      this.results.push({
        testName,
        passed,
        message: passed ? "通过" : errorMessage,
      });

      console.log(`${passed ? "✅" : "❌"} ${testName}`);
    } catch (error) {
      this.results.push({
        testName,
        passed: false,
        message: `测试执行失败: ${error}`,
      });

      console.log(`❌ ${testName} - 执行失败`);
    }
  }

  /**
   * 生成测试报告
   */
  generateReport(): string {
    const passedTests = this.results.filter((r) => r.passed).length;
    const totalTests = this.results.length;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);

    let report = `# RBAC系统测试报告\n\n`;
    report += `## 测试概览\n`;
    report += `- 总测试数: ${totalTests}\n`;
    report += `- 通过测试: ${passedTests}\n`;
    report += `- 失败测试: ${totalTests - passedTests}\n`;
    report += `- 通过率: ${passRate}%\n\n`;

    report += `## 详细结果\n\n`;

    this.results.forEach((result) => {
      const status = result.passed ? "✅ 通过" : "❌ 失败";
      report += `### ${result.testName}\n`;
      report += `**状态**: ${status}\n`;
      report += `**说明**: ${result.message}\n\n`;
    });

    return report;
  }
}

// 在开发环境中将测试工具挂载到全局对象上
if (process.env.NODE_ENV === "development") {
  (window as any).RBACTester = RBACTester;

  // 自动运行测试（延迟执行，确保其他模块已加载）
  setTimeout(() => {
    console.log(
      "🔧 RBAC测试工具已加载，使用 new RBACTester().runAllTests() 运行测试"
    );
  }, 2000);
}

export default RBACTester;
