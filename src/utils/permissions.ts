import type { Permission, UserRole } from "@/store/userStore";
import { useAuthStore } from "@/store/authStore";

// 路由权限映射配置
export const ROUTE_PERMISSIONS: Record<string, Permission[]> = {
  // 工作台 - 所有用户都可以访问
  "/dashboard": [],

  // 项目管理
  "/project": ["view_project"],
  "/project/create": ["create-project"],
  "/project/:id": ["view_project"],

  // 任务管理
  "/task": ["view_task"], // 至少需要查看自己的任务
  "/task/create": ["create_task"],
  "/task/:id": ["view_task"], // 可以查看自己的任务，其他权限在组件内判断

  // 用户管理
  "/user": ["manage_users"],

  // 系统设置
  "/settings": ["system_settings"],

  // AI模型管理
  "/ai-model": ["manager_model"], // 需要系统设置权限

  // 提示词管理
  "/ai-prompt": ["manager_prompt"], // 需要系统设置权限
};

// 角色权限映射（从userStore复制，保持一致性）
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  ROLE_ADMIN: [
    "manage_users",
    "view_users",
    "create-project",
    "create_task",
    "assign_task",
    "submitAIAudit",
    "review_documents",
    "view_project",
    "view_project_detail",
    "view_task_detail",
    "view_task",
    "view_prompt",
    "view_model",
    "manager_prompt",
    "manager_model",
    "update_issue",
    "system_settings",
    "manage_team",
  ],
  ROLE_LEADER: [
    "view_users",
    "create-project",
    "create_task",
    "assign_task",
    "submitAIAudit",
    "review_documents",
    "view_project",
    "view_project_detail",
    "view_task_detail",
    "view_task",
    "view_prompt",
    "view_model",
    "manager_prompt",
    "update_issue",
  ],
  ROLE_MEMBER: [
    "view_project_detail",
    "view_users",
    "view_project",
    "update_issue",
    "view_task",
    "view_task_detail",
    "view_prompt",
    "view_model",
  ],
};

// 权限检查工具函数
export class PermissionUtils {
  /**
   * 检查用户是否拥有指定权限（任意一个）
   */
  static hasAnyPermission(
    userPermissions: Permission[],
    requiredPermissions: Permission[]
  ): boolean {
    if (!requiredPermissions.length) return true; // 如果不需要权限，直接通过
    if (!userPermissions?.length) return false; // 如果用户没有权限，拒绝

    return requiredPermissions.some((permission) =>
      userPermissions.includes(permission)
    );
  }

  /**
   * 检查用户是否拥有所有指定权限
   */
  static hasAllPermissions(
    userPermissions: Permission[],
    requiredPermissions: Permission[]
  ): boolean {
    if (!requiredPermissions.length) return true; // 如果不需要权限，直接通过
    if (!userPermissions?.length) return false; // 如果用户没有权限，拒绝

    return requiredPermissions.every((permission) =>
      userPermissions.includes(permission)
    );
  }

  /**
   * 检查用户是否可以访问指定路由
   */
  static canAccessRoute(userPermissions: Permission[], route: string): boolean {
    // 处理动态路由，将参数替换为通配符
    const normalizedRoute = route.replace(/\/[^/]+$/, "/:id");
    const requiredPermissions =
      ROUTE_PERMISSIONS[route] || ROUTE_PERMISSIONS[normalizedRoute] || [];

    return this.hasAnyPermission(userPermissions, requiredPermissions);
  }

  /**
   * 根据角色获取权限列表
   */
  static getPermissionsByRole(role: UserRole): Permission[] {
    return ROLE_PERMISSIONS[role] || [];
  }

  /**
   * 检查用户角色是否有权限
   */
  static roleHasPermission(role: UserRole, permission: Permission): boolean {
    const rolePermissions = this.getPermissionsByRole(role);
    return rolePermissions.includes(permission);
  }

  /**
   * 获取当前用户权限
   */
  static getCurrentUserPermissions(): Permission[] {
    const userInfo = useAuthStore.getState().userInfo;
    return userInfo?.permissions || [];
  }

  /**
   * 获取当前用户角色
   */
  static getCurrentUserRole(): UserRole[] | null {
    const userInfo = useAuthStore.getState().userInfo;
    return userInfo?.roles || null;
  }

  /**
   * 检查当前用户是否有指定权限
   */
  static currentUserHasPermission(permission: Permission): boolean {
    const userPermissions = this.getCurrentUserPermissions();
    return userPermissions.includes(permission);
  }

  /**
   * 检查当前用户是否有任意指定权限
   */
  static currentUserHasAnyPermission(permissions: Permission[]): boolean {
    const userPermissions = this.getCurrentUserPermissions();
    return this.hasAnyPermission(userPermissions, permissions);
  }

  /**
   * 检查当前用户是否可以访问路由
   */
  static currentUserCanAccessRoute(route: string): boolean {
    const userPermissions = this.getCurrentUserPermissions();
    return this.canAccessRoute(userPermissions, route);
  }
}

// React Hook：检查权限
export function usePermissions() {
  const userInfo = useAuthStore((state) => state.userInfo);
  const userPermissions = userInfo?.permissions || [];
  const userRoles = userInfo?.roles || "ROLE_MEMBER";

  return {
    permissions: userPermissions,
    role: userRoles,
    hasPermission: (permission: Permission) =>
      userPermissions.includes(permission),
    hasAnyPermission: (permissions: Permission[]) =>
      PermissionUtils.hasAnyPermission(userPermissions, permissions),
    hasAllPermissions: (permissions: Permission[]) =>
      PermissionUtils.hasAllPermissions(userPermissions, permissions),
    canAccessRoute: (route: string) =>
      PermissionUtils.canAccessRoute(userPermissions, route),
  };
}

// React Hook：检查特定权限
export function useHasPermission(permission: Permission): boolean {
  const userInfo = useAuthStore((state) => state.userInfo);
  return userInfo?.permissions?.includes(permission) || false;
}

// React Hook：检查多个权限（任意一个）
export function useHasAnyPermission(permissions: Permission[]): boolean {
  const userInfo = useAuthStore((state) => state.userInfo);
  const userPermissions = userInfo?.permissions || [];
  return PermissionUtils.hasAnyPermission(userPermissions, permissions);
}

// React Hook：检查路由访问权限
export function useCanAccessRoute(route: string): boolean {
  const userInfo = useAuthStore((state) => state.userInfo);
  const userPermissions = userInfo?.permissions || [];
  return PermissionUtils.canAccessRoute(userPermissions, route);
}
