import { useEffect, useState, Suspense } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Clock,
  CheckCircle,
  TrendingUp,
  Plus,
  FolderOpen,
} from "lucide-react";
import { Link } from "react-router-dom";
import PageHeader from "@/components/PageHeader";
import { useAuthStore } from "@/store/authStore";
import { Progress } from "@/components/ui/progress";
import { useProjectStore } from "@/store/projectStore";

// 创建一个包装组件来处理需要使用useSearchParams的部分
const Dashboard: React.FC = () => {
  const { userInfo } = useAuthStore();
  const { dashboardInfo, fetchDashboardInfo } = useProjectStore();

  useEffect(() => {
    fetchDashboardInfo();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "进行中":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "待分配":
      case "待开始":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "已完成":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";
    }
  };

  return (
    <>
      <PageHeader
        title={`工作台${userInfo ? ` - 欢迎，${userInfo.name}` : ""}`}
        description="这里是您的工作概览"
      />
      <div className="p-6 flex-grow bg-muted/20">
        {" "}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* {stats.map((stat, index) => ( */}
          <Card>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    项目总数
                  </p>
                  <p className="text-3xl font-bold text-foreground">
                    {dashboardInfo?.projectCount}
                  </p>
                </div>
                <div className={`p-3 rounded-full bg-purple-100`}>
                  <FolderOpen className={`h-6 w-6 text-purple-600`} />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    任务总数
                  </p>
                  <p className="text-3xl font-bold text-foreground">
                    {dashboardInfo?.taskCount}
                  </p>
                </div>
                <div className={`p-3 rounded-full bg-blue-100`}>
                  <FileText className={`h-6 w-6 text-blue-600`} />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    待处理
                  </p>
                  <p className="text-3xl font-bold text-foreground">
                    {dashboardInfo?.totalIssueCount ||
                      0 - (dashboardInfo?.completeIssueCount || 0)}
                  </p>
                </div>
                <div className={`p-3 rounded-full bg-yellow-100`}>
                  <Clock className={`h-6 w-6 text-yellow-600`} />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    已完成
                  </p>
                  <p className="text-3xl font-bold text-foreground">
                    {dashboardInfo?.completeIssueCount}
                  </p>
                </div>
                <div className={`p-3 rounded-full bg-green-100`}>
                  <CheckCircle className={`h-6 w-6 text-green-600`} />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 最近项目 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-left pb-2">最近项目</CardTitle>
                <CardDescription>最新创建的审核项目</CardDescription>
              </div>
              <Link to="/project">
                <Button variant="outline" size="sm">
                  查看全部
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardInfo?.projectList.map((project) => (
                  <div
                    key={project.id}
                    className="flex items-start sm:items-center justify-between p-4 border rounded-lg flex-col sm:flex-row gap-4 sm:gap-0"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1 sm:mb-2">
                        <h4 className="font-medium text-foreground">
                          {project.projectName}
                        </h4>
                        <Badge className={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2 line-clamp-1 text-left">
                        {project.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>
                          {project.completedTasks || 0}/{project.taskCount || 0}{" "}
                          任务
                        </span>
                        <span>{project.createdTime}</span>
                      </div>
                    </div>
                    <div className="ml-0 sm:ml-4 w-full sm:w-auto">
                      <Progress value={project.progress} className="w-16" />
                      <span className="text-xs text-muted-foreground">
                        {project.progress || 0}% 完成
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 最近任务 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-left pb-2">最近任务</CardTitle>
                <CardDescription>最新创建的审核任务</CardDescription>
              </div>
              <Link to="/task">
                <Button variant="outline" size="sm">
                  查看全部
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardInfo?.taskList.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-start sm:items-center justify-between p-4 border rounded-lg flex-col sm:flex-row gap-2 sm:gap-0"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1 sm:mb-2">
                        <h4 className="font-medium text-foreground">
                          {task.taskName}
                        </h4>
                        <Badge variant="outline">{task.fileType}</Badge>
                      </div>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-sm text-muted-foreground">
                        <span>项目: {task.projectName}</span>
                        <span>负责人: {task.reviewer}</span>
                        <span>{task.createdTime}</span>
                      </div>
                    </div>
                    <div className="flex flex-row sm:flex-col gap-2 mt-2 sm:mt-0">
                      <Badge className={task.statusObject.color}>
                        {task.statusObject.label}
                      </Badge>
                      <Badge className={task.priorityObject.color}>
                        {task.priorityObject.label}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        {/* 快速操作 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>常用功能快捷入口</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link to="/project/create">
                <Button
                  className="w-full justify-start h-auto p-4 text-left"
                  variant="outline"
                >
                  <Plus className="mr-3 h-5 w-5 shrink-0" />
                  <div>
                    <div className="font-medium">创建新项目</div>
                    <div className="text-sm text-muted-foreground">
                      创建文档审核项目
                    </div>
                  </div>
                </Button>
              </Link>

              <Link to="/project">
                <Button
                  className="w-full justify-start h-auto p-4 text-left"
                  variant="outline"
                >
                  <FolderOpen className="mr-3 h-5 w-5 shrink-0" />
                  <div>
                    <div className="font-medium">项目管理</div>
                    <div className="text-sm text-muted-foreground">
                      查看和管理所有项目
                    </div>
                  </div>
                </Button>
              </Link>

              <Link to="/task">
                <Button
                  className="w-full justify-start h-auto p-4 text-left"
                  variant="outline"
                >
                  <FileText className="mr-3 h-5 w-5 shrink-0" />
                  <div>
                    <div className="font-medium">任务管理</div>
                    <div className="text-sm text-muted-foreground">
                      查看和管理所有任务
                    </div>
                  </div>
                </Button>
              </Link>

              <Button
                className="w-full justify-start h-auto p-4 text-left"
                variant="outline"
              >
                <TrendingUp className="mr-3 h-5 w-5 shrink-0" />
                <div>
                  <div className="font-medium">数据报告</div>
                  <div className="text-sm text-muted-foreground">
                    查看审核工作统计报告
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default Dashboard;
