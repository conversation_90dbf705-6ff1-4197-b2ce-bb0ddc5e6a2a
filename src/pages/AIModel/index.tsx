import PageHeader from "@/components/PageHeader"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { MoreHorizontal, Edit, Trash2 } from "lucide-react"
import { useAIModelStore, type AIModel } from "@/store/aiStore"
import { useEffect, useState } from "react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import httpClient from "@/utils/httpClient"
import { Label } from "@/components/ui/label"

const AIModelPage: React.FC = () => {
  const {
    models,
    loading,
    fetchModels,
  } = useAIModelStore()

  const [editOpen, setEditOpen] = useState(false)
  const [editModelId, setEditModelId] = useState("")
  const [editToken, setEditToken] = useState("")
  const [editUrl, setEditUrl] = useState("")

  useEffect(() => {
    fetchModels()
  }, [fetchModels])

  const handleEditModel = (model: AIModel) => {
    setEditModelId(model.modelId)
    setEditToken(model.token)
    setEditUrl(model.apiurl)
    setEditOpen(true)
  }

  const handleEditModelSave = async () => {
    await httpClient.post("/ai/model/modify", {
      modelId: editModelId,
      token: editToken,
      apiurl: editUrl
    })
    setEditOpen(false)
    setEditModelId("")
    setEditToken("")
    setEditUrl("")
    fetchModels()
  }

  const handleDeleteModel = (modelId: string) => {
    console.log(modelId)
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        <PageHeader title="大模型管理" description="大模型管理" />
        <div className="p-6">
          <Card>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>模型</TableHead>
                    <TableHead>模型名称</TableHead>
                    <TableHead>供应商</TableHead>
                    <TableHead>Token</TableHead>
                    <TableHead>API地址</TableHead>
                    <TableHead>模型更新时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    // 加载中的骨架屏
                    Array(5).fill(0).map((_, index) => (
                      <TableRow key={`skeleton-${index}`}>
                        <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-8" /></TableCell>
                        <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                        <TableCell><Skeleton className="h-8 w-8 rounded-full" /></TableCell>
                      </TableRow>
                    ))
                  ) : models.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        没有找到模型
                      </TableCell>
                    </TableRow>
                  ) : (
                    models.map((model) => (
                      <TableRow key={model.modelId}>
                        <TableCell>{model.modelId}</TableCell>
                        <TableCell>{model.name}</TableCell>
                        <TableCell>{model.orgnizer}</TableCell>
                        <TableCell>{model.token}</TableCell>
                        <TableCell>{model.apiurl}</TableCell>
                        <TableCell>{model.updatedTime}</TableCell>
                        <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEditModel(model)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑模型
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDeleteModel(model.modelId!)} className="text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除模型
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            </Card>
          </div>
      </div>
      <Dialog open={editOpen} onOpenChange={(open) => {
        setEditOpen(open)
        if (!open) {
          setEditModelId("")
          setEditToken("")
          setEditUrl("")
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑模型</DialogTitle>
          </DialogHeader>
          <Label className="mb-1">Token</Label>
          <Input
            placeholder="请输入Token"
            value={editToken}
            onChange={e => setEditToken(e.target.value)}
            className="mb-4"
          />
          <Label className="mb-1">API地址</Label>
          <Input
            placeholder="请输入API地址"
            value={editUrl}
            onChange={e => setEditUrl(e.target.value)}
          />
          <DialogFooter>
            <Button variant="outline" onClick={() => {setEditOpen(false); setEditModelId(""); setEditToken(""); setEditUrl("")}}>取消</Button>
            <Button onClick={handleEditModelSave} disabled={!editToken.trim() || !editUrl.trim()}>确定</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AIModelPage