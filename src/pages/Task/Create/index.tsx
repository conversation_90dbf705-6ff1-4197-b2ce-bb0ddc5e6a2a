import React, { useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { useState, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import PageHeader from "@/components/PageHeader"
import {
  Upload,
  FileText,
  User,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Loader2,
  FolderOpen,
  Brain,
  MessageCircle,
  Cloud
} from "lucide-react"
import httpClient from '@/utils/httpClient'
import { useUserStore } from "@/store/userStore"
import { useAIModelStore } from "@/store/aiStore"

const TaskCreate: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const projectId = searchParams.get("projectId") || ""

  const fileInputRef = useRef<HTMLInputElement>(null)

  const [currentStep, setCurrentStep] = useState(1)
  const [isUploading, setIsUploading] = useState(false)
  const [isParsing, setIsParsing] = useState(false)
  const { users, fetchUsers } = useUserStore()
  const { models, fetchModels, prompts, fetchPrompts } = useAIModelStore()

  useEffect(() => {
    fetchUsers()
    fetchModels()
    fetchPrompts()
  }, [fetchUsers, fetchModels, fetchPrompts])

  const teamMembers = users.filter(user => 
    user.role === 'manager' || user.role === 'user'
  ).map(user => ({
    ...user,
    // 确保workload属性存在
    workload: user.workload || user.tasksCount || 0
  }))

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: "",
    deadline: "",
    assignee: "",
    model: "",
    prompt: "",
    file: null as File | null,
    fileUrl: "",
    fileId: "",
  })

  const [dragActive, setDragActive] = useState(false)

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0])
    }
  }

  const handleFileUpload = async (file: File) => {
    if (
      file.type !== "application/pdf" &&
      file.type !== "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      alert("请上传 PDF 或 DOCX 格式的文件")
      return
    }

    setFormData({ ...formData, file, title: file.name.split(".")[0] })
    setIsUploading(true)

    const aFormData = new FormData()
    aFormData.append("file", file)
    
    const response = await httpClient.upload('file/upload', aFormData, (percentComplete) => {
    // setUploadProgress(prev => ({ ...prev, [docId]: percentComplete }))
      console.log(`上传进度: ${percentComplete}%`)
    })
      
    const { fileUrl } = response
    console.log("上传成功:", fileUrl)

    setIsUploading(false)
    setFormData((prev) => ({ ...prev, fileUrl }))
  }

  const handleUploadToAliyun = async () => {
    const aFormData = new FormData()
    aFormData.append("file", formData.file!)

    const response = await httpClient.upload('file/uploadToAliyun', aFormData)

    const { fileId } = response
    setFormData((prev) => ({ ...prev, fileId }))
    console.log("上传至阿里云成功:", response)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (
      !formData.title ||
      !formData.file ||
      !formData.priority ||
      !formData.deadline ||
      !formData.assignee ||
      !formData.model ||
      !formData.prompt
    ) {
      alert("请填写所有必填字段")
      return
    }

    // 模拟创建任务
    console.log("创建任务:", formData)


    const reviewer = teamMembers.find(member => member.name === formData.assignee)
    console.log('审核员信息', reviewer);

    const taskData = {
      projectId,
      taskName: formData.title,
      description: formData.description,
      bookUrl: formData.fileUrl,
      deadline: formData.deadline + " 00:00:00",
      priority: formData.priority === "高" ? 0 : formData.priority === "中" ? 1 : 2,
      fileName: formData.file?.name,
      reviewerId: reviewer?.userId || "",
      reviewer: formData.assignee,
      modelId: formData.model,
      promptId: formData.prompt,
      fileId: formData.fileId,
    }

    const response = await httpClient.post('task/saveTask', taskData)
    console.log("任务创建成功:", response)

    alert("任务创建成功！")

    // 根据是否有项目ID决定跳转目标
    if (projectId) {
      navigate(`/project/${projectId}`)
    } else {
      navigate("/task")
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case "senior_reviewer":
        return "bg-purple-100 text-purple-800"
      case "reviewer":
        return "bg-blue-100 text-blue-800"
      case "junior_reviewer":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "在线":
        return "bg-green-100 text-green-800"
      case "忙碌":
        return "bg-yellow-100 text-yellow-800"
      case "离线":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case "senior_reviewer":
        return "高级审核员"
      case "reviewer":
        return "审核员"
      case "junior_reviewer":
        return "初级审核员"
      default:
        return role
    }
  }

  const getWorkloadColor = (workload: number) => {
    if (workload <= 2) return "text-green-600"
    if (workload <= 5) return "text-yellow-600"
    return "text-red-600"
  }

  const steps = [
    { id: 1, name: "上传文档", description: "选择并上传需要审核的文档" },
    { id: 2, name: "配置任务", description: "设置任务信息和分配审核员" },
    { id: 3, name: "完成创建", description: "确认信息并创建任务" },
  ]

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        {projectId && (
          <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
            <div className="flex items-center gap-2 text-blue-800">
              <FolderOpen className="h-4 w-4" />
              <span className="text-sm">正在为项目添加任务</span>
              <Link to={`/project/${projectId}`} className="text-blue-600 hover:text-blue-800 underline">
                返回项目详情
              </Link>
            </div>
          </div>
        )}
        <PageHeader
          title="创建审核任务"
          description="上传文档并配置审核任务"
          badge={{
            text: `步骤 ${currentStep}/3`,
            variant: "outline",
          }}
        />

        <div className="p-6">
          {/* 步骤指示器 */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                      currentStep >= step.id
                        ? "bg-blue-600 border-blue-600 text-white"
                        : "border-gray-300 text-gray-500"
                    }`}
                  >
                    {currentStep > step.id ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm font-medium text-left ${currentStep >= step.id ? "text-blue-600" : "text-gray-500"}`}>
                      {step.name}
                    </p>
                    <p className="text-xs text-gray-500">{step.description}</p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${currentStep > step.id ? "bg-blue-600" : "bg-gray-300"}`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="max-w-full">
            {/* 步骤1: 文件上传 */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="h-5 w-5" />
                    上传文档
                  </CardTitle>
                  <CardDescription>支持 PDF 和 DOCX 格式，最大 100MB</CardDescription>
                </CardHeader>
                <CardContent>
                  <div
                    className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
                      dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    {isUploading ? (
                      <div className="flex flex-col items-center">
                        <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-4" />
                        <p className="text-lg font-medium text-gray-900 mb-2">正在上传文件...</p>
                        <Progress value={75} className="w-64" />
                      </div>
                    ) : isParsing ? (
                      <div className="flex flex-col items-center">
                        <Loader2 className="h-12 w-12 text-green-500 animate-spin mb-4" />
                        <p className="text-lg font-medium text-gray-900 mb-2">正在解析文档...</p>
                        <p className="text-sm text-gray-600">这可能需要几分钟时间</p>
                        <Progress value={45} className="w-64 mt-2" />
                      </div>
                    ) : formData.file ? (
                      <div className="flex flex-col items-center">
                        <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                        <p className="text-lg font-medium text-gray-900 mb-2">文件上传成功</p>
                        <div className="flex items-center gap-2 mb-4">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <span className="font-medium">{formData.file.name}</span>
                          <Badge variant="outline">{(formData.file.size / 1024 / 1024).toFixed(2)} MB</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-4">文件ID：{formData.fileId}</p>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setFormData({ ...formData, file: null })}
                        >
                          重新选择文件
                        </Button>
                        <div className="flex flex-row gap-2 mt-4">
                          <Button
                            type="button"
                            variant="secondary"
                            onClick={handleUploadToAliyun}
                              >
                            <Cloud className="h-5 w-5 mr-2" />
                            上传文件至阿里云
                          </Button>
                          <Button
                            type="button"
                            onClick={() => setCurrentStep(2)}
                          >
                            下一步，配置任务
                            <ChevronRight className="ml-2 h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <Upload className="mx-auto h-16 w-16 text-gray-400 mb-6" />
                        <p className="text-xl font-medium text-gray-900 mb-2">拖拽文件到此处或点击上传</p>
                        <p className="text-gray-600 mb-6">支持 PDF 和 DOCX 格式</p>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept=".pdf,.docx"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                        <Button type="button" onClick={() => fileInputRef.current?.click()}>
                          选择文件
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 步骤2: 配置任务 */}
            {currentStep === 2 && (
              <div className="space-y-6">
                {/* 基本信息 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      基本信息
                    </CardTitle>
                    <CardDescription className="text-left">填写任务的基本信息</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className='pb-2' htmlFor="title">任务标题 *</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                        placeholder="请输入任务标题"
                        className={!formData.title ? "border-red-300" : ""}
                      />
                      {!formData.title && <p className="text-sm text-red-600 mt-1">请输入任务标题</p>}
                    </div>

                    <div>
                      <Label className='pb-2' htmlFor="description">任务描述</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        placeholder="请描述审核要求和注意事项"
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className='pb-2' htmlFor="priority">优先级 *</Label>
                        <Select
                          value={formData.priority}
                          onValueChange={(value) => setFormData({ ...formData, priority: value })}
                        >
                          <SelectTrigger className={!formData.priority ? "border-red-300" : ""}>
                            <SelectValue placeholder="选择优先级" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="高">高</SelectItem>
                            <SelectItem value="中">中</SelectItem>
                            <SelectItem value="低">低</SelectItem>
                          </SelectContent>
                        </Select>
                        {!formData.priority && <p className="text-sm text-red-600 mt-1 text-left">请选择优先级</p>}
                      </div>

                      <div>
                        <Label className='pb-2' htmlFor="deadline">截止日期 *</Label>
                        <Input
                          id="deadline"
                          type="date"
                          value={formData.deadline}
                          onChange={(e) => setFormData({ ...formData, deadline: e.target.value })}
                          className={!formData.deadline ? "border-red-300" : ""}
                        />
                        {!formData.deadline && <p className="text-sm text-red-600 mt-1 text-left">请选择截止日期</p>}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 分配审核员 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      分配审核员 *
                    </CardTitle>
                    <CardDescription className="text-left">选择负责此任务的审核员</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className='pb-2' htmlFor="assignee">选择审核员</Label>
                      <Select
                        value={formData.assignee}
                        onValueChange={(value) => setFormData({ ...formData, assignee: value })}
                      >
                        <SelectTrigger className={!formData.assignee ? "border-red-300" : ""}>
                          <SelectValue placeholder="请选择审核员">
                            {formData.assignee && (
                              <div className="flex items-center gap-2">
                                {(() => {
                                  const selectedMember = teamMembers.find((member) => member.name === formData.assignee)
                                  if (!selectedMember) return null
                                  return (
                                    <>
                                      <img
                                        src={selectedMember.avatar || "/placeholder.svg"}
                                        alt={selectedMember.name}
                                        className="w-6 h-6 rounded-full object-cover"
                                      />
                                      <span>{selectedMember.name}</span>
                                    </>
                                  )
                                })()}
                              </div>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {teamMembers.map((member) => (
                            <SelectItem key={member.id} value={member.name}>
                              <div className="flex items-center gap-3 py-1">
                                <img
                                  src={member.avatar || "/placeholder.svg"}
                                  alt={member.name}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{member.name}</span>
                                    <Badge className={getStatusColor(member.status)} variant="outline">
                                      {member.status}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center gap-2 text-xs text-gray-500">
                                    <Badge className={getRoleColor(member.role)} variant="secondary">
                                      {getRoleLabel(member.role)}
                                    </Badge>
                                    <span>{member.department}</span>
                                    <span className={getWorkloadColor(member.workload)}>{member.workload}个任务</span>
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {!formData.assignee && <p className="text-sm text-red-600 mt-1 text-left">请选择一位审核员</p>}
                    </div>

                    {/* 已选择审核员的详细信息 */}
                    {formData.assignee && (
                      <div className="mt-4">
                        {(() => {
                          const selectedMember = teamMembers.find((member) => member.name === formData.assignee)
                          if (!selectedMember) return null

                          return (
                            <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                              <div className="flex items-start gap-4">
                                <img
                                  src={selectedMember.avatar || "/placeholder.svg"}
                                  alt={selectedMember.name}
                                  className="w-16 h-16 rounded-full object-cover"
                                />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <h4 className="text-lg font-semibold text-gray-900">{selectedMember.name}</h4>
                                    <Badge className={getStatusColor(selectedMember.status)}>
                                      {selectedMember.status}
                                    </Badge>
                                  </div>

                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                      <span className="text-gray-600">角色：</span>
                                      <Badge className={getRoleColor(selectedMember.role)} variant="secondary">
                                        {getRoleLabel(selectedMember.role)}
                                      </Badge>
                                    </div>
                                    <div>
                                      <span className="text-gray-600">部门：</span>
                                      <span className="font-medium">{selectedMember.department}</span>
                                    </div>
                                    <div>
                                      <span className="text-gray-600">当前任务：</span>
                                      <span className={`font-medium ${getWorkloadColor(selectedMember.workload)}`}>
                                        {selectedMember.workload} 个
                                      </span>
                                    </div>
                                    <div>
                                      <span className="text-gray-600">工作经验：</span>
                                      <span className="font-medium">{selectedMember.tasksCount}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        })()}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* 选择模型 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="h-5 w-5" />
                      {/* <Settings className="h-5 w-5" /> */}
                      选择模型 *
                    </CardTitle>
                    <CardDescription className="text-left">选择适合的模型，系统将根据模型进行智能检查</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {!formData.model && <p className="text-sm text-red-600 mb-4 text-left">请选择一个模型</p>}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      {models.map((model) => (
                        <div
                          key={model.modelId}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            formData.model === model.modelId
                              ? "border-blue-500 bg-blue-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => setFormData({ ...formData, model: model.modelId })}
                        >
                          <h4 className="font-medium text-gray-900 text-center">{model.modelId}</h4>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* 选择提示词 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageCircle className="h-5 w-5" />
                      选择提示词 *
                    </CardTitle>
                    <CardDescription className="text-left">选择适合的提示词，系统将根据提示词进行智能检查</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {!formData.prompt && <p className="text-sm text-red-600 mb-4 text-left">请选择一个提示词</p>}
                    <Select value={formData.prompt} onValueChange={(value) => setFormData({ ...formData, prompt: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择一个提示词" />
                      </SelectTrigger>
                      <SelectContent>
                        {prompts.map((prompt) => (
                          <SelectItem key={prompt.promptId} value={prompt.promptId}>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{prompt.type}</Badge>
                              <span className="w-180 truncate">{prompt.text}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </CardContent>
                </Card>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep(1)}>
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    上一步
                  </Button>
                  <Button
                    onClick={() => setCurrentStep(3)}
                    disabled={
                      !formData.title ||
                      !formData.priority ||
                      !formData.deadline ||
                      !formData.assignee ||
                      !formData.model ||
                      !formData.prompt
                    }
                  >
                    下一步
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* 步骤3: 确认创建 */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-left">确认任务信息</CardTitle>
                    <CardDescription className="text-left">请确认以下信息无误后创建任务</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 text-left">基本信息</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex">
                              <span className="text-gray-600">任务标题:</span>
                              <span className="ml-2">{formData.title}</span>
                            </div>
                            <div className="flex">
                              <span className="text-gray-600">优先级:</span>
                              <Badge className="ml-2">{formData.priority}</Badge>
                            </div>
                            <div className="flex">
                              <span className="text-gray-600">截止日期:</span>
                              <span className="ml-2">{formData.deadline}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 text-left">文档信息</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex gap-2">
                              <span className="text-gray-600">文件名:</span>
                              <span>{formData.file?.name}</span>
                            </div>
                            <div className="flex gap-2">
                              <span className="text-gray-600">文件ID:</span>
                              <span>{formData.fileId}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 text-left">分配信息</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex">
                              <span className="text-gray-600">审核员:</span>
                              <span className="ml-2">{formData.assignee}</span>
                            </div>
                            <div className="flex">
                              <span className="text-gray-600">模型:</span>
                              <span className="ml-2">{models.find((m) => m.modelId === formData.model)?.modelId}</span>
                            </div>
                            <div className="flex">
                              <span className="text-gray-600">提示词:</span>
                            </div>
                            <div className="flex text-left">
                                {prompts.find((p) => p.promptId === formData.prompt)?.text}
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 text-left">任务描述</h4>
                          <p className="text-sm text-gray-600 text-left">{formData.description || "无特殊说明"}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep(2)}>
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    上一步
                  </Button>
                  <Button onClick={handleSubmit}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    创建任务
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="p-6 mt-4 flex justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => (projectId ? navigate(`/project/${projectId}`) : navigate("/task"))}
          >
            取消
          </Button>
        </div>
      </div>
    </div>
  )
}

export default TaskCreate; 