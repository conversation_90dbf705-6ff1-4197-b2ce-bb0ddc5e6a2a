import { use<PERSON>ara<PERSON> } from 'react-router-dom';
import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import MarkdownRenderer from "@/components/MarkdownRenderer"
import { useSidebar } from "@/components/ui/sidebar"
import { AlertTriangle, MessageSquare, Download, RefreshCcw, ChevronUp, Plus, UploadCloud } from "lucide-react"
import PageHeader from "@/components/PageHeader"
import { useTaskStore } from '@/store/taskStore'
import { useAIModelStore } from '@/store/aiStore';
import httpClient from '@/utils/httpClient';
import { Di<PERSON>, DialogContent, Di<PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

const TaskDetail: React.FC = () => {
  const params = useParams()
  const taskId = params.id as string
  const { state } = useSidebar()

  const {
    currentTask: task,
    loading,
    error,
    fetchTaskDetail
  } = useTaskStore()

  const { fetchModels, fetchPrompts, prompts, models } = useAIModelStore()

  const [selectedIssue, setSelectedIssue] = useState<string | null>(null)
  const [newComment, setNewComment] = useState("")
  const [currentPage] = useState(1)
  const [showComments, setShowComments] = useState(false)
  const [leftScrolledToBottom, setLeftScrolledToBottom] = useState(false)
  const [rightScrolledToBottom, setRightScrolledToBottom] = useState(false)

  const leftScrollRef = useRef<HTMLDivElement>(null)
  const rightScrollRef = useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const [aiDialogOpen, setAIDialogOpen] = useState(false)
  const [selectedModel, setSelectedModel] = useState("")
  const [selectedPrompt, setSelectedPrompt] = useState("")
  const [showAddPrompt, setShowAddPrompt] = useState(false)
  const [newPromptText, setNewPromptText] = useState("")
  const [newPromptType, setNewPromptType] = useState("")

  const [filterModelId, setFilterModelId] = useState<string | null>(null)
  const [filterPromptId, setFilterPromptId] = useState<string | null>(null)

  const [localIssues, setLocalIssues] = useState<any[]>(task?.issues ?? [])

  // 收集所有modelId和promptId
  const allModelIds = Array.from(new Set((task?.issues ?? []).map((issue: any) => issue.modelId).filter(Boolean)))
  const allPromptIds = Array.from(new Set((task?.issues ?? []).map((issue: any) => issue.promptId).filter(Boolean)))

  // 问题列表筛选
  const filteredIssues = (localIssues ?? []).filter((issue: any) => {
    if (filterModelId && issue.modelId !== filterModelId) return false
    if (filterPromptId && issue.promptId !== filterPromptId) return false
    return true
  })

  // 状态label到value的映射表
  const STATUS_LABEL_TO_VALUE: Record<string, number> = {
    "待处理": 0,
    "已修复": 1,
    "已忽略": 2,
  }

  const fileInputRef = useRef<HTMLInputElement>(null)

  const [showCheckFile, setShowCheckFile] = useState(false)

  const isPDF = !!task?.checkFileUrl && task.checkFileUrl.toLowerCase().endsWith('.pdf')
  const isDOCX = !!task?.checkFileUrl && (task.checkFileUrl.toLowerCase().endsWith('.docx') || task.checkFileUrl.toLowerCase().endsWith('.doc'))

  useEffect(() => {
    fetchModels()
    fetchPrompts()
  }, [])

  useEffect(() => {
    if (taskId) {
      fetchTaskDetail(taskId)
    }
  }, [taskId])

  useEffect(() => {
    setLocalIssues(task?.issues ?? [])
  }, [task?.issues])

  const comments = [
    {
      id: 1,
      author: "张三",
      content: "已完成第一轮审核，发现10个问题需要修正。重点关注第1页和第3页的问题。",
      timestamp: "2024-01-16 10:30",
      type: "审核意见",
    },
    {
      id: 2,
      author: "管理员",
      content: "请重点关注专业术语的准确性，特别是技术规格部分。",
      timestamp: "2024-01-15 14:20",
      type: "任务说明",
    },
    {
      id: 3,
      author: "张三",
      content: "第2页的格式问题已修复，请确认。",
      timestamp: "2024-01-16 11:15",
      type: "进度更新",
    },
    {
      id: 4,
      author: "张三",
      content: `正在审核第${currentPage}页，发现了一些需要注意的问题。`,
      timestamp: "2024-01-16 14:20",
      type: "进度更新",
    },
    {
      id: 5,
      author: "李四",
      content: "建议在技术规格部分增加更详细的说明，帮助用户更好地理解产品特性。",
      timestamp: "2024-01-16 15:30",
      type: "建议",
    },
    {
      id: 6,
      author: "管理员",
      content: "请在完成当前页面审核后，重点检查第5页的专业术语使用是否规范。",
      timestamp: "2024-01-16 16:45",
      type: "任务说明",
    },
  ]

  // 防抖处理的滚动检测函数
  const checkScrollPosition = useCallback((element: HTMLDivElement, setScrolledToBottom: (value: boolean) => void) => {
    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // 设置新的定时器，防抖处理
    scrollTimeoutRef.current = setTimeout(() => {
      const { scrollTop, scrollHeight, clientHeight } = element

      // 使用更大的容差值，并且使用百分比计算
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight
      const isAtBottom = scrollPercentage >= 0.95 // 滚动到95%就认为是底部

      setScrolledToBottom(isAtBottom)
    }, 100) // 100ms 防抖
  }, [])

  // 监听滚动事件
  useEffect(() => {
    const leftElement = leftScrollRef.current
    const rightElement = rightScrollRef.current

    const leftScrollHandler = () => {
      if (leftElement) {
        checkScrollPosition(leftElement, setLeftScrolledToBottom)
      }
    }

    const rightScrollHandler = () => {
      if (rightElement) {
        checkScrollPosition(rightElement, setRightScrolledToBottom)
      }
    }

    if (leftElement) {
      leftElement.addEventListener("scroll", leftScrollHandler, { passive: true })
    }

    if (rightElement) {
      rightElement.addEventListener("scroll", rightScrollHandler, { passive: true })
    }

    // 清理函数
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      if (leftElement) {
        leftElement.removeEventListener("scroll", leftScrollHandler)
      }
      if (rightElement) {
        rightElement.removeEventListener("scroll", rightScrollHandler)
      }
    }
  }, [checkScrollPosition])

  // 当任一侧滚动到底部时显示评论
  useEffect(() => {
    setShowComments(leftScrolledToBottom || rightScrolledToBottom)
  }, [leftScrolledToBottom, rightScrolledToBottom])

  // 页面切换时重置滚动状态
  useEffect(() => {
    setLeftScrolledToBottom(false)
    setRightScrolledToBottom(false)
    setShowComments(false)

    // 重置滚动位置
    if (leftScrollRef.current) {
      leftScrollRef.current.scrollTop = 0
    }
    if (rightScrollRef.current) {
      rightScrollRef.current.scrollTop = 0
    }
  }, [currentPage, state])

  const getIssueColor = (severity: string = "中") => {
    switch (severity) {
      case "高":
        return "bg-red-100 text-red-800 border-red-200"
      case "中":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "低":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }
  
  const getTaskStatus = (status: number) => {
    switch (status) {
      case 0:
        return "AI审核中"
      case 1:
        return "待人工复核"
      case 2:
        return "已完成"
      default:
        return "未知"
    }
  }

  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return "待处理"
      case 1:
        return "已修复"
      case 2:
        return "已忽略"
      default:
        return "未知"
    }
  }

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return "bg-yellow-100 text-yellow-800"
      case 1:
        return "bg-green-100 text-green-800"
      case 2:
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleOptimizePrompt = () => {
    setAIDialogOpen(true)
  }

  const handleReAudit = async () => {
    await httpClient.post('/task/submitAIAudit', { taskId, modelId: selectedModel, promptId: selectedPrompt, promptText: newPromptText, promptType: newPromptType })
    alert("已重新发起AI审核，请稍后刷新页面查看")
  }

  const handleIssueClick = (issueId: string) => {
    setSelectedIssue(selectedIssue === issueId ? null : issueId)
  }

  const handleStatusChange = async (issueId: string, newStatus: number) => {
    await httpClient.post('ai/issue/update', { issueId, status: newStatus })
    setLocalIssues(prev =>
      prev.map(issue =>
        issue.issueId === issueId ? { ...issue, status: newStatus } : issue
      )
    )
    console.log(`更新问题 ${issueId} 状态为: ${newStatus}`)
  }

  const handleUploadHandwritten = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('上传文件', e.target.files)
    const file = e.target.files?.[0]
    if (!file) return
    const formData = new FormData()
    formData.append("file", file)
    const resFile = await httpClient.upload('file/upload', formData)
    const { fileUrl } = resFile
    console.log('上传成功', fileUrl)
    const resUpdate = await httpClient.post('task/update/checkFile', { taskId, fileUrl })
    console.log('提交成功', resUpdate)
    alert('上传成功')
  }

  const handleAddComment = () => {
    if (newComment.trim()) {
      console.log("添加评论:", newComment)
      setNewComment("")
    }
  }

  const handleHideComments = () => {
    setShowComments(false)
    setLeftScrolledToBottom(false)
    setRightScrolledToBottom(false)
    // 滚动到顶部
    if (leftScrollRef.current) {
      leftScrollRef.current.scrollTop = 0
    }
    if (rightScrollRef.current) {
      rightScrollRef.current.scrollTop = 0
    }
  }

  // 高亮处理函数（根据originText）
  function highlightMarkdownByOriginText(content: string, originText?: string) {
    if (!originText) return content
    const idx = content.indexOf(originText)
    if (idx === -1) return content
    return (
      content.slice(0, idx) +
      "<mark id='highlight-mark' class='bg-yellow-200 text-black rounded px-1'>" +
      content.slice(idx, idx + originText.length) +
      "</mark>" +
      content.slice(idx + originText.length)
    )
  }

  // 渲染Markdown时处理高亮
  const selectedIssueObj = task?.issues?.find(issue => issue.issueId === selectedIssue)
  const highlightedContent = highlightMarkdownByOriginText(
    task?.content || '',
    selectedIssueObj?.originText
  )

  useEffect(() => {
    if (!selectedIssue) return
    setTimeout(() => {
      const mark = document.getElementById("highlight-mark")
      if (mark && leftScrollRef.current) {
        const container = leftScrollRef.current
        const markRect = mark.getBoundingClientRect()
        const containerRect = container.getBoundingClientRect()
        const offset = markRect.top - containerRect.top + container.scrollTop
        container.scrollTo({
          top: offset - container.clientHeight / 2 + mark.clientHeight / 2,
          behavior: "smooth"
        })
      }
    }, 100)
  }, [selectedIssue])

  // 重置AI参数弹窗内容的函数
  function resetAIDialog() {
    setSelectedModel("")
    setSelectedPrompt("")
    setShowAddPrompt(false)
    setNewPromptText("")
    setNewPromptType("")
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        {/* 任务头部信息 */}
        {loading && <div className="text-center py-8 text-gray-500">加载中...</div>}
        {error && <div className="text-center py-8 text-red-500">{error}</div>}
        {task && (
          <PageHeader
            title={task.taskName}
            description={`任务ID: #${task.taskId} | ${task.fileName || ''} | 负责人: ${task.reviewer || ''} | 截止: ${task.deadline || ''}`}
            badge={{
              text: getTaskStatus(task.status),
              variant: task.status === 1 ? "default" : task.status === 2 ? "destructive" : "secondary",
            }}
            actions={
              <>
                <Badge variant="outline">{task.priorityObject.label || "中"}</Badge>
                <Button variant="secondary" size="sm" onClick={() => {
                  const link = document.createElement("a")
                  link.href = task.bookUrl
                  link.download = task.fileName || "book.docx"
                  link.click()
                }}>
                  <Download className="mr-2 h-4 w-4" />
                  下载原文件
                </Button>

                <Button variant="default" size="sm" onClick={handleUploadHandwritten}>
                  <UploadCloud className="mr-2 h-4 w-4" />
                  上传手写标注稿
                </Button>
              </>
            }
          />
        )}

        <div className="p-6">
          {/* 主要内容区域 - 固定高度的网格布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6" style={{ height: "calc(100vh - 200px)" }}>
            {/* 左侧：文档预览 - 相对定位容器 */}
            <div className="relative overflow-hidden">
              <Card className="h-full flex flex-col">
                {/* 文档内容显示区域 - 可滚动，底部留出页码空间 */}
                <div
                  ref={leftScrollRef}
                  className="flex-1 overflow-y-auto px-6 py-4"
                  style={{ paddingBottom: "120px" }}
                >
                  <div className="bg-white min-h-full">
                    <MarkdownRenderer content={highlightedContent} className="markdown-content" />
                  </div>
                </div>

                {/* 滚动到底部的提示 - 固定高度避免布局抖动 */}
                <div className="absolute bottom-20 left-0 right-0 h-16 flex items-center justify-center">
                  <div
                    className={`transition-opacity duration-300 ${leftScrolledToBottom ? "opacity-100" : "opacity-0"}`}
                  >
                    <div className="bg-white/90 backdrop-blur-sm border rounded-lg px-4 py-2 shadow-sm">
                      <div className="flex items-center gap-2 text-gray-500">
                        <MessageSquare className="h-4 w-4" />
                        <span className="text-sm">已滚动到底部，查看下方审核意见</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* 右侧：问题列表 - 独立滚动容器 */}
            <div className="flex flex-col h-full overflow-hidden">
              <Card className="flex-1 flex flex-col min-h-0">
                <CardHeader className="flex-shrink-0 border-b">
                  <CardTitle className="flex items-center justify-between">
                    <div className='flex flex-col gap-2'>
                      <span className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5" /> 问题列表 ({filteredIssues.length})
                        {task?.checkFileUrl && (
                          <Button variant='link' className='underline' onClick={() => setShowCheckFile(true)}>
                            查看手写标注
                          </Button>
                        )}
                      </span>
                      <div className="flex items-center gap-2 flex-wrap">
                        <Badge
                          variant={!filterPromptId ? "default" : "secondary"}
                          onClick={() => setFilterPromptId(null)}
                          className="cursor-pointer"
                        >全部提示词</Badge>
                        {allPromptIds.map(promptId => (
                          <Badge
                            key={promptId}
                            variant={filterPromptId === promptId ? "default" : "secondary"}
                            onClick={() => setFilterPromptId(filterPromptId === promptId ? null : promptId)}
                            className="cursor-pointer"
                          >{promptId}</Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-2 flex-wrap">
                        <Badge
                          variant={!filterModelId ? "default" : "secondary"}
                          onClick={() => setFilterModelId(null)}
                          className="cursor-pointer"
                        >全部模型</Badge>
                        {allModelIds.map(modelId => (
                          <Badge
                            key={modelId}
                            variant={filterModelId === modelId ? "default" : "secondary"}
                            onClick={() => setFilterModelId(filterModelId === modelId ? null : modelId)}
                            className="cursor-pointer"
                          >{modelId}</Badge>
                        ))}
                      </div>
                    </div>
                    <Button size="sm" onClick={handleOptimizePrompt}>
                      调整AI参数
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-y-auto p-4 relative" ref={rightScrollRef}>
                  <div className="space-y-3" style={{ paddingBottom: "60px" }}>
                    {filteredIssues.length > 0 ? (
                      filteredIssues.map((issue: any) => (
                        <div
                          key={issue.issueId}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            selectedIssue === issue.issueId
                              ? "border-blue-500 bg-blue-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => handleIssueClick(issue.issueId)}
                        >
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge className={getIssueColor(issue.severity)}>{issue.severity || "中"}</Badge>
                              <span className="text-sm font-medium">{issue.issueType}</span>
                            </div>
                            <Badge className={getStatusColor(issue.status)}>{getStatusText(issue.status)}</Badge>
                          </div>

                          <div className="space-y-2 text-sm text-left">
                            <div>
                              <span className="text-gray-600">原文: </span>
                              <span className="bg-red-100 px-1 rounded">{issue.originText}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">建议: </span>
                              <span className="bg-green-100 px-1 rounded">{issue.shortSuggestion}</span>
                            </div>
                            <p className="text-gray-600">{issue.fullSuggestion}</p>
                          </div>

                          {selectedIssue === issue.issueId && (
                            <div className="mt-3 pt-3 border-t">
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-600">状态:</span>
                                <Select
                                  value={getStatusText(issue.status)}
                                  onValueChange={(label) => handleStatusChange(issue.issueId, STATUS_LABEL_TO_VALUE[label])}
                                >
                                  <SelectTrigger className="w-32 h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="待处理">待处理</SelectItem>
                                    <SelectItem value="已修复">已修复</SelectItem>
                                    <SelectItem value="已忽略">已忽略</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12 text-gray-500">
                        <AlertTriangle className="mx-auto h-12 w-12 mb-4 opacity-30" />
                        <p className="text-lg font-medium mb-2">暂无问题</p>
                        <Button onClick={handleReAudit}>
                          <RefreshCcw className="mr-2 h-4 w-4" />
                          重新发起AI审核
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* 滚动到底部的提示 - 固定位置避免布局抖动 */}
                  <div className="absolute bottom-4 left-4 right-4 h-12 flex items-center justify-center">
                    <div
                      className={`transition-opacity duration-300 ${rightScrolledToBottom ? "opacity-100" : "opacity-0"}`}
                    >
                      <div className="bg-white/90 backdrop-blur-sm border rounded-lg px-4 py-2 shadow-sm">
                        <div className="flex items-center gap-2 text-gray-500">
                          <MessageSquare className="h-4 w-4" />
                          <span className="text-sm">已滚动到底部，查看下方审核意见</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* 审核意见区域 - 滑动显示 */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden ${
              showComments ? "max-h-96 opacity-100 mt-6" : "max-h-0 opacity-0"
            }`}
          >
            <Card>
              <CardHeader className="border-b">
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    审核意见 ({comments.length})
                  </span>
                  <Button variant="outline" size="sm" onClick={handleHideComments}>
                    <ChevronUp className="mr-2 h-4 w-4" />
                    收起
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="max-h-80 overflow-y-auto p-4">
                <div className="space-y-4 mb-4">
                  {comments.map((comment) => (
                    <div key={comment.id} className="border-l-4 border-blue-500 pl-4 flex flex-col gap-2">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">{comment.author}</span>
                        <span className="text-xs text-gray-500">{comment.timestamp}</span>
                      </div>
                      <p className="text-sm text-gray-700 text-left">{comment.content}</p>
                      <Badge variant="outline" className="mt-1 text-xs">
                        {comment.type}
                      </Badge>
                    </div>
                  ))}
                </div>

                <div className="space-y-2 border-t pt-4">
                  <Textarea
                    placeholder="添加审核意见..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    rows={3}
                  />
                  <Button onClick={handleAddComment} size="sm">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    添加意见
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Dialog open={aiDialogOpen} onOpenChange={(open) => {
        setAIDialogOpen(open)
        if (!open) {
          resetAIDialog()
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>调整AI参数</DialogTitle>
          </DialogHeader>
          <div className="mb-4">
            <Label className="mb-2">选择模型</Label>
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger>
                <SelectValue placeholder="请选择模型" />
              </SelectTrigger>
              <SelectContent>
                {models.map(model => (
                  <SelectItem key={model.modelId} value={model.modelId}>{model.modelId}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="mb-4 flex items-center gap-2">
            <div className="w-full min-w-0">
              <Label className="mb-2">选择提示词</Label>
              <Select value={selectedPrompt} onValueChange={v => { setSelectedPrompt(v); if (v) setShowAddPrompt(false); }}>
                <SelectTrigger className="max-w-[400px] truncate">
                  <SelectValue placeholder="请选择提示词" className="truncate" />
                </SelectTrigger>
                <SelectContent className="max-w-[400px]">
                  {prompts.map((prompt: any) => (
                    <SelectItem key={prompt.promptId} value={prompt.promptId} className="truncate max-w-[380px]">
                      {prompt.text}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button size="icon" variant="secondary" className='rounded-full' onClick={() => { setShowAddPrompt(true); setSelectedPrompt(""); }}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          {/* 仅当未选择提示词且点击添加时显示输入框 */}
          {showAddPrompt && !selectedPrompt && (
            <div className="mb-4">
              <Label className='mb-2'>提示词内容</Label>
              <Textarea
                value={newPromptText}
                onChange={e => setNewPromptText(e.target.value)}
                placeholder="请输入提示词内容"
                className="mb-2 min-h-[100px] max-h-[200px] overflow-y-auto"
              />
              <Label className='mb-2'>类型</Label>
              <Input
                value={newPromptType}
                onChange={e => setNewPromptType(e.target.value)}
                placeholder="请输入类型"
              />
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => { setAIDialogOpen(false); resetAIDialog(); }}>取消</Button>
            <Button onClick={() => { handleReAudit(); resetAIDialog(); setAIDialogOpen(false); }}>{ showAddPrompt ? '添加提示词并' : '' }重新发起审核</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <input
        type="file"
        accept=".pdf,.docx,.doc"
        ref={fileInputRef}
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <Dialog open={showCheckFile} onOpenChange={setShowCheckFile}>
        <DialogContent className="max-w-6xl h-[90vh]">
          <DialogHeader>
            <DialogTitle>手写标注预览</DialogTitle>
          </DialogHeader>
          {isPDF ? (
            <iframe
              src={task?.checkFileUrl || ''}
              title="PDF预览"
              className="w-full h-[80vh] border rounded"
            />
          ) : isDOCX ? (
            <iframe
              src={`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(task?.checkFileUrl || '')}`}
              title="DOCX预览"
              className="w-full h-[80vh] border rounded"
            />
          ) : (
            <div>暂不支持该文件类型在线预览</div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default TaskDetail; 