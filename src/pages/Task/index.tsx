import { useState, useEffect, use<PERSON>emo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import PageHeader from "@/components/PageHeader";
import { useTaskStore } from "@/store/taskStore";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useAuthStore } from "@/store/authStore";
import { useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";

interface Project {
  id: string;
  name: string;
}

const Task: React.FC = () => {
  const { tasks, filters, loading, error, fetchTasks, setFilters, deleteTask } =
    useTaskStore();
  const { userInfo } = useAuthStore();
  const [filterMyTasks, setFilterMyTasks] = useState(false);
  const navigate = useNavigate();
  // 从 tasks 中获取所有项目信息并去重
  const projects: Project[] = useMemo(() => {
    const projectMap = new Map<string, Project>();

    tasks.forEach((task) => {
      if (task.projectId && task.projectName) {
        projectMap.set(task.projectId, {
          id: task.projectId,
          name: task.projectName,
        });
      }
    });

    return Array.from(projectMap.values());
  }, [tasks]);

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  const filteredTasks = tasks.filter((task) => {
    const matchesSearch =
      task.taskName?.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      task.reviewer?.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      task.projectName
        ?.toLowerCase()
        .includes(filters.searchTerm.toLowerCase());
    const matchesStatus =
      filters.statusFilter === "all" ||
      String(task.status) === filters.statusFilter;
    const matchesPriority =
      filters.priorityFilter === "all" ||
      String(task.priority) === filters.priorityFilter;
    const matchesProject =
      filters.projectFilter === "all" ||
      task.projectId === filters.projectFilter;
    const matchesMyTasks = filterMyTasks
      ? task.reviewerId === userInfo?.userId
      : true;
    return (
      matchesSearch &&
      matchesStatus &&
      matchesPriority &&
      matchesProject &&
      matchesMyTasks
    );
  });

  const handleSearch = (v: string) => setFilters({ searchTerm: v });
  const handleStatus = (v: string) => setFilters({ statusFilter: v });
  const handlePriority = (v: string) => setFilters({ priorityFilter: v });
  const handleProject = (v: string) => setFilters({ projectFilter: v });

  const handleDeleteTask = async (taskId: string) => {
    await deleteTask(taskId);
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* The lg:ml-64 class is removed as the new global sidebar handles layout */}
      <div className="flex-1">
        <PageHeader
          title="所有任务"
          description="查看和管理所有项目中的任务"
          actions={
            <Link to="/task/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建任务
              </Button>
            </Link>
          }
        />

        <div className="p-6">
          <div className="mb-8">
            <Card>
              <CardContent>
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="搜索任务标题、负责人或项目..."
                        value={filters.searchTerm}
                        onChange={(e) => handleSearch(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Select
                      value={filters.projectFilter}
                      onValueChange={handleProject}
                    >
                      <SelectTrigger className=" sm:w-40">
                        <SelectValue placeholder="项目" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有项目</SelectItem>
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Select
                      value={filters.statusFilter}
                      onValueChange={handleStatus}
                    >
                      <SelectTrigger className="w-full sm:w-32">
                        <SelectValue placeholder="状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="0">AI审核中</SelectItem>
                        <SelectItem value="1">待人工复核</SelectItem>
                        <SelectItem value="2">已完成</SelectItem>
                        <SelectItem value="3">未知</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select
                      value={filters.priorityFilter}
                      onValueChange={handlePriority}
                    >
                      <SelectTrigger className="w-full sm:w-32">
                        <SelectValue placeholder="优先级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部优先级</SelectItem>
                        <SelectItem value="0">高</SelectItem>
                        <SelectItem value="1">中</SelectItem>
                        <SelectItem value="2">低</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="flex items-center gap-2">
                      <Label>我参与的</Label>
                      <Switch
                        checked={filterMyTasks}
                        onCheckedChange={setFilterMyTasks}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="p-0 overflow-hidden">
            {/* <CardHeader>
              <CardTitle className="text-2xl text-left">
                任务列表 ({filteredTasks.length})
              </CardTitle>
            </CardHeader> */}
            <CardContent className="px-0">
              <Table className="overflow-x-auto">
                <TableHeader className="bg-muted">
                  <TableRow>
                    <TableHead></TableHead>
                    <TableHead>任务标题</TableHead>
                    <TableHead>所属项目</TableHead>
                    <TableHead>文件信息</TableHead>
                    <TableHead>文件类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>优先级</TableHead>
                    <TableHead>负责人</TableHead>
                    <TableHead>截止日期</TableHead>
                    <TableHead>进度</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks.map((task) => (
                    <TableRow
                      key={task.id}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => navigate(`/task/${task.taskId}`)}
                    >
                      <TableCell className="w-10"></TableCell>
                      <TableCell className="font-medium truncate max-w-[200px]">
                        {task.taskName}
                      </TableCell>
                      <TableCell className="truncate max-w-[200px]">
                        <Link
                          to={`/project/${task.projectId}`}
                          className="text-blue-600 hover:underline"
                        >
                          {task.projectName}
                        </Link>
                      </TableCell>
                      <TableCell className="truncate max-w-[200px]">
                        {task.fileName || "-"}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{task.fileType}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={task.statusObject.color}>
                          {task.statusObject.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={task.priorityObject.color}>
                          {task.priorityObject.label}
                        </Badge>
                      </TableCell>
                      <TableCell className="truncate max-w-[100px]">
                        {task.reviewer}
                      </TableCell>
                      <TableCell>{task.deadline}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={task.progress} className="w-12" />
                          <span className="text-sm text-gray-600">
                            {task.progress}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                title="更多操作"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                分配任务
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                下载文档
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteTask(task.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除任务
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {filteredTasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  没有找到符合条件的任务。
                </div>
              )}
              {loading && (
                <div className="text-center py-8 text-gray-500">加载中...</div>
              )}
              {error && (
                <div className="text-center py-8 text-red-500">{error}</div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Task;
