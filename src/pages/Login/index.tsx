import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Eye, EyeOff } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import FeishuLogin from "@/components/FeishuLogin"
import { useNavigate, useSearchParams } from "react-router-dom"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { useAuthStore } from "@/store/authStore"

const Login: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("account")
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  })
  const { isAuthenticated, loading, loginByPassword } = useAuthStore()

  useEffect(() => {
    console.log("isAuthenticated", isAuthenticated)
    console.log("loading", loading)
    
    // 如果已经登录，重定向到工作台
    if (isAuthenticated && !loading) {
      navigate('/dashboard')
    }
  }, [isAuthenticated, loading, navigate])

  useEffect(() => {
    // 检查URL中是否有错误参数
    const errorParam = searchParams.get("error")
    if (errorParam) {
      switch (errorParam) {
        case "config":
          setError("飞书应用配置错误，请联系管理员")
          break
        case "token":
          setError("获取飞书访问令牌失败")
          break
        case "user":
          setError("获取用户信息失败")
          break
        case "details":
          setError("获取用户详细信息失败")
          break
        default:
          setError("登录过程中出现未知错误")
          break
      }
    }
  }, [searchParams])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    const result = await loginByPassword(formData.username, formData.password)
    if (result.success) {
      navigate("/dashboard")
    } else {
      setError(result.error || "登录失败")
    }
  }

  // 飞书登录成功后会自动跳转到回调URL，不需要在这里处理
  const handleFeishuLoginSuccess = (data: any) => {
    console.log("飞书扫码成功，等待跳转...", data)
    // 飞书SDK会自动处理跳转，不需要在这里手动跳转
  }

  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-lg">正在加载...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">文字审核系统</CardTitle>
          <CardDescription>请登录您的账户</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>登录失败</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="account">账号密码登录</TabsTrigger>
              <TabsTrigger value="feishu">飞书扫码登录</TabsTrigger>
            </TabsList>
            <TabsContent value="account">
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">用户名</Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="请输入用户名"
                    value={formData.username}
                    onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">密码</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="请输入密码"
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <Button type="submit" className="w-full">
                  登录
                </Button>
              </form>
            </TabsContent>
            <TabsContent value="feishu" className="flex justify-center">
              {activeTab === "feishu" && <FeishuLogin onSuccess={handleFeishuLoginSuccess} />}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

export default Login;