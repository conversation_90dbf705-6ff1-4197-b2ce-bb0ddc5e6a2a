import PageHeader from "@/components/PageHeader"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, Edit, Trash2, Plus } from "lucide-react"
import { useAIModelStore, type AIPrompt } from "@/store/aiStore"
import { useEffect, useState } from "react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import httpClient from "@/utils/httpClient"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

const AIPromptPage: React.FC = () => {
  const {
    prompts,
    loading,
    fetchPrompts,
  } = useAIModelStore()

  const [open, setOpen] = useState(false)
  const [promptText, setPromptText] = useState("")
  const [promptType, setPromptType] = useState("")
  const [edit, setEdit] = useState(false)
  const [editPromptId, setEditPromptId] = useState("")

  useEffect(() => {
    fetchPrompts()
  }, [fetchPrompts])

  const handleEditPrompt = (prompt: AIPrompt) => {
    setEdit(true)
    setPromptText(prompt.text)
    setPromptType(prompt.type)
    setEditPromptId(prompt.promptId!)
    setOpen(true)
  }

  const handleDeletePrompt = async (promptId: string) => {
    await httpClient.post("/ai/prompt/delete", { promptId: promptId })
    fetchPrompts()
  }

  const handleAddPrompt = async () => {
    if (!promptText.trim() || !promptType.trim()) return
    if (edit) {
      await httpClient.post("/ai/prompt/modify", { promptId: editPromptId, prompt: promptText, type: promptType })
    } else {
      await httpClient.post("/ai/prompt/save", { prompt: promptText, type: promptType })
    }
    setOpen(false)
    setPromptText("")
    setPromptType("")
    fetchPrompts()
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        <PageHeader
          title="提示词管理"
          description="提示词管理"
          actions={
            <Button onClick={() => {setOpen(true); setEdit(false)}}>
              <Plus className="mr-2 h-4 w-4" />
              添加提示词
            </Button>
          }
        />
        <div className="p-6">
          <Card>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>提示词ID</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>提示词内容</TableHead>
                    <TableHead>得分</TableHead>
                    <TableHead>使用次数</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    // 加载中的骨架屏
                    Array(5).fill(0).map((_, index) => (
                      <TableRow key={`skeleton-${index}`}>
                        <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-8" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-8" /></TableCell>
                      </TableRow>
                    ))
                  ) : prompts.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        没有找到提示词
                      </TableCell>
                    </TableRow>
                  ) : (
                    prompts.map((prompt) => (
                      <TableRow key={prompt.promptId}>
                        <TableCell className="w-20 truncate">{prompt.promptId}</TableCell>
                        <TableCell className="w-20">
                          {prompt.type && <Badge variant="outline">{prompt.type}</Badge>}
                        </TableCell>
                        <TableCell className="max-w-[300px] truncate">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span>{prompt.text}</span>
                              </TooltipTrigger>
                              <TooltipContent
                                align="start"
                                className="max-w-[400px] whitespace-pre-line break-words bg-gray-50 text-gray-900 shadow-lg border border-gray-200"
                              >
                                {prompt.text}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="w-20">{prompt.score}</TableCell>
                        <TableCell className="w-20">{prompt.count}</TableCell>
                        <TableCell className="w-20">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEditPrompt(prompt)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑提示词
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDeletePrompt(prompt.promptId!)} className="text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除提示词
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            </Card>
          </div>
      </div>
      <Dialog open={open} onOpenChange={(open) => {
        if (!open) {
          setPromptText("")
          setPromptType("")
        }
        setOpen(open)
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加提示词</DialogTitle>
          </DialogHeader>
          <Label className="mt-2">提示词内容</Label>
          <Textarea
            placeholder="请输入提示词内容"
            value={promptText}
            onChange={e => setPromptText(e.target.value)}
            className="h-32 resize-y"
          />
          <Label className="mt-2">类型</Label>
          <Input
            placeholder="请输入类型"
            value={promptType}
            onChange={e => setPromptType(e.target.value)}
          />
          <DialogFooter>
            <Button variant="outline" onClick={() => {setOpen(false); setPromptText(""); setPromptType("")}}>
              取消
            </Button>
            <Button onClick={handleAddPrompt} disabled={!promptText.trim() || promptType?.trim() === ""}>
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AIPromptPage