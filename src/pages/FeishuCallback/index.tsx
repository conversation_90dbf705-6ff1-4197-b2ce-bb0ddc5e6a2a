import { useEffect, useState } from "react";
import { useNavigate, useSearchParams, useLocation } from "react-router-dom";
import { useAuthStore } from "@/store/authStore";
import { Loader2 } from "lucide-react";

const FeishuCallback = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { login } = useAuthStore();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // 获取授权码
        const code = searchParams.get("code");
        console.log("飞书授权成功 code", code)
        
        // 获取原始目标路径（如果存在）
        const redirectPath = sessionStorage.getItem("auth_redirect_path") || "/dashboard";
        
        if (!code) {
          setError("未找到授权码，请重新登录");
          setIsProcessing(false);
          return;
        }

        console.log("收到飞书授权码:", code);
        
        // 调用登录接口
        const result = await login(code);
        
        if (result.success) {
          // 登录成功，跳转到原始目标页面
          console.log("登录成功，跳转到:", redirectPath);
          // 清除存储的重定向路径
          sessionStorage.removeItem("auth_redirect_path");
          navigate(redirectPath);
        } else {
          // 登录失败，显示错误信息
          setError(result.error || "登录失败，请重试");
          setIsProcessing(false);
        }
      } catch (error) {
        console.error("处理飞书回调失败:", error);
        setError("登录过程中发生错误，请重试");
        setIsProcessing(false);
      }
    };

    handleCallback();
  }, [searchParams, login, navigate]);

  // 如果正在处理，显示加载状态
  if (isProcessing) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-lg">正在处理飞书登录...</p>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h2 className="text-xl font-semibold text-red-600 mb-4">登录失败</h2>
          <p className="text-gray-700 mb-6">{error}</p>
          <button
            className="w-full bg-primary text-white py-2 px-4 rounded hover:bg-primary/90"
            onClick={() => navigate("/login")}
          >
            返回登录页
          </button>
        </div>
      </div>
    );
  }

  return null;
};

export default FeishuCallback; 