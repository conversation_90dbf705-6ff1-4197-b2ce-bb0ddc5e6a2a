"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  ArrowLeft,
  Users,
  FileText,
  FolderOpen,
  Loader2,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import PageHeader from "@/components/PageHeader";
import { useParams, useNavigate } from "react-router-dom";
import { useProjectStore } from "@/store/projectStore";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";

const ProjectDetail: React.FC = () => {
  const params = useParams();
  const navigate = useNavigate();
  const projectId = params.id as string;

  const { currentProject, loading, error, fetchProjectDetail } =
    useProjectStore();

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [assigneeFilter, setAssigneeFilter] = useState("all");

  // 获取项目详情
  useEffect(() => {
    fetchProjectDetail(projectId);
  }, [projectId, fetchProjectDetail]);

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="mx-auto h-12 w-12 animate-spin text-blue-500" />
          <p className="mt-4 text-lg text-gray-600">正在加载项目详情...</p>
        </div>
      </div>
    );
  }

  // 如果加载失败，显示错误信息
  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-xl text-red-500 mb-4">{error}</p>
          <Button onClick={() => fetchProjectDetail(projectId)}>重试</Button>
        </div>
      </div>
    );
  }

  // 如果项目不存在，显示提示信息
  if (!currentProject) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-xl text-gray-600 mb-4">项目不存在或已被删除</p>
          <Link to="/project">
            <Button>返回项目列表</Button>
          </Link>
        </div>
      </div>
    );
  }

  // 使用currentProject替代模拟数据
  const project = currentProject;

  // 使用项目中的任务数据，需要进行类型适配
  const tasks = project.tasks
    ? project.tasks.map((task) => ({
        ...task,
        type: task.bookUrl
          ? task.bookUrl.toLowerCase().endsWith(".pdf")
            ? "PDF"
            : "DOCX"
          : "DOCX",
      }))
    : [];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "高":
        return "bg-red-100 text-red-800";
      case "中":
        return "bg-yellow-100 text-yellow-800";
      case "低":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredTasks = tasks.filter((task) => {
    const matchesSearch =
      task.taskName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.fileName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.reviewer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || task.statusObject.label === statusFilter;
    const matchesAssignee =
      assigneeFilter === "all" || task.reviewerId === assigneeFilter;

    return matchesSearch && matchesStatus && matchesAssignee;
  });

  const handleDeleteTask = (taskId: string | number) => {
    if (confirm("确定要删除这个任务吗？")) {
      console.log("删除任务:", taskId);
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        <PageHeader
          title={project.projectName}
          description={project.description}
          badge={{
            text: project.status,
            variant:
              project.status === "已完成"
                ? "default"
                : project.status === "暂停"
                ? "destructive"
                : "secondary",
          }}
          actions={
            <>
              <Link to="/project">
                <Button variant="outline">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  返回项目列表
                </Button>
              </Link>
              <Link to={`/task/create?projectId=${projectId}`}>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  添加任务
                </Button>
              </Link>
            </>
          }
        />

        <div className="p-6">
          {/* 项目概览 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      总任务数
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      {project.taskCount}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-blue-100">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">已完成</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {project.completedTasks}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-green-100">
                    <FileText className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">总页数</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {project.totalPages}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-purple-100">
                    <FolderOpen className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">审核员</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {project.reviewerCount}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-orange-100">
                    <Users className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 项目信息和团队 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-left">
                  项目信息
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center flex-row">
                    <span className="text-gray-600">创建者:</span>
                    <div className="flex items-center gap-2 ml-2">
                      {project.createUser && project.createUser.avatar ? (
                        <img
                          src={project.createUser.avatar}
                          alt={project.createUser.name}
                          className="w-6 h-6 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-medium">
                          {project.creatorName?.charAt(0) ||
                            project.creator?.charAt(0) ||
                            "?"}
                        </div>
                      )}
                      <span className="font-medium">
                        {project.createUser?.name ||
                          project.creatorName ||
                          project.creator ||
                          "未知"}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center flex-row">
                    <span className="text-gray-600">优先级:</span>
                    <Badge
                      className={`ml-2 ${getPriorityColor(project.priority)}`}
                    >
                      {project.priority}
                    </Badge>
                  </div>
                  <div className="flex items-center flex-row">
                    <span className="text-gray-600">创建时间:</span>
                    <span className="ml-2 font-medium">
                      {project.createdTime}
                    </span>
                  </div>
                  <div className="flex items-center flex-row">
                    <span className="text-gray-600">截止时间:</span>
                    <span className="ml-2 font-medium">{project.deadline}</span>
                  </div>
                  <div className="col-span-2 flex flex-col">
                    <span className="text-gray-600 text-left">完成进度:</span>
                    <div className="mt-2">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">
                          {project.progress}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all"
                          style={{ width: `${project.progress}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-left">
                  审核员
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {project.reviewers && project.reviewers.length > 0 ? (
                    project.reviewers.map((reviewer, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <Avatar className="w-8 h-8 rounded-full object-cover">
                          <AvatarImage
                            src={reviewer.avatar}
                            alt={reviewer.name}
                          />
                          <AvatarFallback>
                            {reviewer.name?.charAt(0) || "?"}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                          <p className="font-medium text-sm text-left">
                            {reviewer.name}
                          </p>
                          <p className="text-xs text-gray-600 text-left">
                            审核员
                          </p>
                        </div>
                      </div>
                    ))
                  ) : project.teamMembers && project.teamMembers.length > 0 ? (
                    project.teamMembers.map((member, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                          {member.charAt(0)}
                        </div>
                        <div>
                          <p className="font-medium text-sm">{member}</p>
                          <p className="text-xs text-gray-600">审核员</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">暂无团队成员</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和筛选 */}
          <Card className="mb-6">
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索任务标题、文件名或负责人..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="待分配">待分配</SelectItem>
                      <SelectItem value="待处理">待处理</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                      <SelectItem value="有问题">有问题</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={assigneeFilter}
                    onValueChange={setAssigneeFilter}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="负责人" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部负责人</SelectItem>
                      <SelectItem value="张三">张三</SelectItem>
                      <SelectItem value="李四">李四</SelectItem>
                      <SelectItem value="王五">王五</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 任务列表 */}
          <Card>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>任务标题</TableHead>
                    <TableHead>文件信息</TableHead>
                    <TableHead>文件类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>优先级</TableHead>
                    <TableHead>负责人</TableHead>
                    <TableHead>截止日期</TableHead>
                    <TableHead>进度</TableHead>
                    <TableHead>问题数</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks.map((task) => (
                    <TableRow
                      key={task.id}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() =>
                        navigate(`/task/${task.taskId}`, {
                          state: { fromProject: true, projectId: projectId },
                        })
                      }
                    >
                      <TableCell className="font-medium">
                        {task.taskName}
                      </TableCell>
                      <TableCell className="truncate max-w-[200px]">
                        {task.fileName}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{task.fileType}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={task.statusObject.color}>
                          {task.statusObject.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={task.priorityObject.color}>
                          {task.priorityObject.label}
                        </Badge>
                      </TableCell>
                      <TableCell>{task.reviewer}</TableCell>
                      <TableCell>{task.deadline}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={task.progress} className="w-12" />
                          <span className="text-sm text-gray-600">
                            {task.progress}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`text-sm ${
                            task.totalIssueCount > 0
                              ? "text-red-600 font-medium"
                              : "text-gray-600"
                          }`}
                        >
                          {task.totalIssueCount}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                编辑任务
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                下载文档
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteTask(task.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除任务
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetail;
