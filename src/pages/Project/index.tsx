import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Plus, Eye, FolderOpen, FileText, Users, Calendar, MoreHorizontal, Loader2 } from "lucide-react"
import { Link } from "react-router-dom"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import PageHeader from "@/components/PageHeader"
import { useProjectStore } from "@/store/projectStore"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"

const Project: React.FC = () => {
  const { 
    projects, 
    filters, 
    loading, 
    error, 
    fetchProjects, 
    setFilters 
  } = useProjectStore()
  
  // 首次加载时获取项目列表
  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ searchTerm: e.target.value })
  }

  const handleStatusFilterChange = (value: string) => {
    setFilters({ statusFilter: value })
  }

  const handlePriorityFilterChange = (value: string) => {
    setFilters({ priorityFilter: value })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "进行中":
        return "bg-blue-100 text-blue-800"
      case "待开始":
        return "bg-gray-100 text-gray-800"
      case "已完成":
        return "bg-green-100 text-green-800"
      case "暂停":
        return "bg-yellow-100 text-yellow-800"
      case "已取消":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "高":
        return "bg-red-100 text-red-800"
      case "中":
        return "bg-yellow-100 text-yellow-800"
      case "低":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress === 100) return "bg-green-500"
    if (progress >= 50) return "bg-blue-500"
    if (progress >= 25) return "bg-yellow-500"
    return "bg-gray-400"
  }

  const filteredProjects = projects.filter((project) => {
    const matchesSearch =
      project.projectName.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      (project.creatorName && project.creatorName.toLowerCase().includes(filters.searchTerm.toLowerCase()))
    const matchesStatus = filters.statusFilter === "all" || project.status === filters.statusFilter
    const matchesPriority = filters.priorityFilter === "all" || project.priority === filters.priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  })

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        <PageHeader
          title="项目列表"
          description="管理所有文档审核项目"
          actions={
            <Link to="/project/create">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建项目
              </Button>
            </Link>
          }
        />

        <div className="p-6">
          {/* 搜索和筛选 */}
          <Card className="mb-6">
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索项目名称、描述或创建者..."
                      value={filters.searchTerm}
                      onChange={handleSearchChange}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={filters.statusFilter} onValueChange={handleStatusFilterChange}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="待开始">待开始</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                      <SelectItem value="暂停">暂停</SelectItem>
                      <SelectItem value="已取消">已取消</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filters.priorityFilter} onValueChange={handlePriorityFilterChange}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部优先级</SelectItem>
                      <SelectItem value="高">高</SelectItem>
                      <SelectItem value="中">中</SelectItem>
                      <SelectItem value="低">低</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 项目网格 */}
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-lg text-gray-600">正在加载项目...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-2">加载失败: {error}</p>
              <Button variant="outline" onClick={() => fetchProjects()}>
                重试
              </Button>
            </div>
          ) : filteredProjects.length === 0 ? (
            <div className="text-center py-12 col-span-3">
              <p className="text-gray-500 mb-2">没有找到匹配的项目</p>
              <Link to="/project/create">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  创建新项目
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredProjects.map((project) => (
                <Link key={project.projectId} to={`/project/${project.projectId}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg mb-2 line-clamp-2 text-left">{project.projectName}</CardTitle>
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                            <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 line-clamp-2 text-left mt-4">{project.description}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={(e) => e.stopPropagation()}>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link to={`/project/${project.id}`} onClick={(e) => e.stopPropagation()}>
                                <Eye className="mr-2 h-4 w-4" />
                                查看详情
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                              <FileText className="mr-2 h-4 w-4" />
                              编辑项目
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {/* 项目统计 */}
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-600">
                            {project.taskCount} 任务
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <FolderOpen className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-600">{project.totalIssueCount} 问题</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-600">{project.reviewerAvatars.length} 人</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-600">{project.deadline}</span>
                        </div>
                      </div>

                      {/* 进度条 */}
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">完成进度</span>
                          <span className="text-sm text-gray-600">{project.progress}%</span>
                        </div>
                        <Progress value={project.progress} className="w-full" />
                      </div>

                      {/* 团队成员头像 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1">
                          <span className="text-xs text-gray-500">团队:</span>
                          <div className="flex -space-x-1">
                            {project.reviewerAvatars.slice(0, 3).map((member, index) => (
                              <Avatar key={index} className="w-6 h-6 rounded-full object-cover border-2 border-white">
                                <AvatarImage src={member} alt={member} />
                                <AvatarFallback>{member.charAt(0)}</AvatarFallback>
                              </Avatar>
                            ))}
                            {project.reviewerAvatars.length > 3 && (
                              <div className="w-6 h-6 rounded-full bg-gray-400 border-2 border-white flex items-center justify-center text-xs text-white font-medium">
                                +{project.reviewerAvatars.length - 3}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Project;