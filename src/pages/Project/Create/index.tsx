import type React from "react";

import { useState, useRef, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import PageHeader from "@/components/PageHeader";
import {
  Upload,
  FileText,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Eye,
  Loader2,
  X,
  Cloud,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useUserStore, type UserRole } from "@/store/userStore";
import { useAuthStore } from "@/store/authStore";
import { useAIModelStore } from "@/store/aiStore";
import { PROJECT_PRIORITY } from "@/store/projectStore";
import httpClient from "@/utils/httpClient";
import type { DocumentFile } from "@/model/document";

const ProjectCreate: React.FC = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { users, fetchUsers } = useUserStore();
  const userInfo = useAuthStore((state) => state.userInfo);
  const { models, fetchModels, prompts, fetchPrompts } = useAIModelStore();

  useEffect(() => {
    fetchUsers();
    fetchModels();
    fetchPrompts();
  }, [fetchUsers]);

  const [currentStep, setCurrentStep] = useState(1);
  const [currentPreviewDoc, setCurrentPreviewDoc] = useState<string | null>(
    null
  );
  const [currentPage, setCurrentPage] = useState(1);

  const [projectData, setProjectData] = useState({
    name: "",
    description: "",
    priority: "",
    deadline: "",
    team: [] as string[],
  });

  const [documents, setDocuments] = useState<DocumentFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {}
  );

  // 获取团队成员（审核员）
  const teamMembers = users
    .filter((user) => user.roles?.includes("ROLE_MEMBER"))
    .map((user) => ({
      ...user,
      // 确保workload属性存在
      workload: 0,
    }));

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files) {
      const files = Array.from(e.dataTransfer.files);
      files.forEach((file) => handleFileUpload(file));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      files.forEach((file) => handleFileUpload(file));
    }
  };

  const handleFileUpload = async (file: File) => {
    // 检查文件类型
    if (
      file.type !== "application/pdf" &&
      file.type !==
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      alert("请上传 PDF 或 DOCX 格式的文件");
      return;
    }

    // 使用时间戳和随机字符串生成唯一ID
    const docId =
      Date.now().toString() + Math.random().toString(36).substr(2, 9);

    // 初始化该文件的上传进度为0
    setUploadProgress((prev) => ({ ...prev, [docId]: 0 }));

    const newDoc: DocumentFile = {
      id: docId,
      file,
      title: file.name.replace(/\.[^/.]+$/, ""),
      fileUrl: "",
      fileId: "",
      assignee: "",
      priority: "",
      deadline: "",
      template: "",
      isUploading: true,
      isParsing: false,
      parsed: false,
      pages: [],
      prompt: "",
      model: "",
      metadata: {
        title: "",
        author: "",
        pageCount: 0,
        wordCount: 0,
        fileSize: "",
      },
    };

    setDocuments((prev) => [...prev, newDoc]);

    const formData = new FormData();
    formData.append("file", file);

    try {
      // 使用httpClient上传文件
      const response = await httpClient.upload(
        "file/upload",
        formData,
        (percentComplete) => {
          setUploadProgress((prev) => ({ ...prev, [docId]: percentComplete }));
          console.log(`上传进度: ${percentComplete}%`);
        }
      );

      const { fileUrl } = response;
      console.log("上传成功:", fileUrl);

      setUploadProgress((prev) => ({ ...prev, [docId]: 100 }));

      // 更新文档状态为解析中
      setDocuments((prev) =>
        prev.map((doc) =>
          doc.id === docId
            ? { ...doc, isUploading: false, isParsing: true, fileUrl }
            : doc
        )
      );

      // 没有元数据，使用模拟数据
      setTimeout(() => {
        const mockPages = Array.from(
          { length: Math.floor(Math.random() * 10) + 5 },
          (_, i) =>
            `# 第${i + 1}页内容\n\n这是${file.name}的第${
              i + 1
            }页内容。\n\n包含了各种文字、段落和格式。`
        );

        setDocuments((prev) =>
          prev.map((doc) =>
            doc.id === docId
              ? {
                  ...doc,
                  isParsing: false,
                  parsed: true,
                  pages: mockPages,
                  metadata: {
                    title: file.name.replace(/\.[^/.]+$/, ""),
                    author: "未知",
                    pageCount: mockPages.length,
                    wordCount: Math.floor(Math.random() * 2000) + 500,
                    fileSize: (file.size / 1024 / 1024).toFixed(2) + " MB",
                  },
                }
              : doc
          )
        );
      }, 2000);

      return response;
    } catch (error) {
      console.error("上传失败:", error);

      // 更新文档状态为上传失败
      setDocuments((prev) =>
        prev.map((doc) =>
          doc.id === docId ? { ...doc, isUploading: false } : doc
        )
      );

      throw error;
    }
  };

  const handleRemoveDocument = (docId: string) => {
    setDocuments((prev) => prev.filter((doc) => doc.id !== docId));
    if (currentPreviewDoc === docId) {
      setCurrentPreviewDoc(null);
    }
  };

  const handleDocumentUpdate = (
    docId: string,
    field: string,
    value: string
  ) => {
    setDocuments((prev) =>
      prev.map((doc) => (doc.id === docId ? { ...doc, [field]: value } : doc))
    );
  };

  const handleUploadToAliyun = async () => {
    documents.forEach(async (doc) => {
      if (doc.fileId) {
        return;
      }
      setDocuments((prev) =>
        prev.map((doc) =>
          doc.id === doc.id ? { ...doc, isUploading: true } : doc
        )
      );
      console.log("上传阿里云的文档:", doc);
      const aFormData = new FormData();
      aFormData.append("file", doc.file);
      const response = await httpClient.upload(
        "file/uploadToAliyun",
        aFormData
      );
      const { fileId } = response;
      console.log("上传至阿里云成功:", response);
      setDocuments((prev) =>
        prev.map((doc) =>
          doc.id === doc.id ? { ...doc, fileId, isUploading: false } : doc
        )
      );
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!projectData.name || !projectData.priority || !projectData.deadline) {
      alert("请填写项目基本信息");
      return;
    }

    if (documents.length === 0) {
      alert("请至少添加一个文档");
      return;
    }

    const incompleteDoc = documents.find(
      (doc) =>
        !doc.assignee ||
        !doc.priority ||
        !doc.deadline ||
        !doc.model ||
        !doc.prompt
    );
    if (incompleteDoc) {
      alert(`请完善文档"${incompleteDoc.title}"的配置信息`);
      return;
    }

    try {
      // 构建请求参数
      const tasks = documents.map((doc) => {
        // 找到对应的审核员信息
        const reviewer = teamMembers.find(
          (member) => member.name === doc.assignee
        );
        console.log("审核员信息", reviewer);

        return {
          taskName: doc.title,
          description: `优先级: ${doc.priority}, 截止日期: ${doc.deadline}`,
          reviewerId: reviewer?.userId || "",
          reviewer: doc.assignee,
          book_url: doc.fileUrl,
          deadline: doc.deadline,
          priority:
            doc.priority === "高"
              ? PROJECT_PRIORITY.HIGH.value
              : doc.priority === "中"
              ? PROJECT_PRIORITY.MEDIUM.value
              : PROJECT_PRIORITY.LOW.value,
          fileName: doc.file.name,
          modelId: doc.model,
          promptId: doc.prompt,
          fileId: doc.fileId,
        };
      });

      // 获取优先级对应的值
      let priorityValue = PROJECT_PRIORITY.MEDIUM.value;
      if (projectData.priority === "高")
        priorityValue = PROJECT_PRIORITY.HIGH.value;
      else if (projectData.priority === "低")
        priorityValue = PROJECT_PRIORITY.LOW.value;

      const requestData = {
        projectName: projectData.name,
        description: projectData.description,
        deadline: projectData.deadline,
        priority: priorityValue,
        creator: userInfo?.userId || "",
        tasks: tasks,
      };

      console.log("提交项目数据:", requestData);

      // 使用httpClient发送请求
      const result = await httpClient.post("project/saveProject", requestData);
      console.log("创建项目结果:", result);

      alert("项目创建成功！");
      navigate("/project");
    } catch (error) {
      console.error("创建项目失败:", error);
      alert(
        `创建项目失败: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case "ROLE_ADMIN":
        return "管理员";
      case "ROLE_LEADER":
        return "组长";
      case "ROLE_MEMBER":
        return "审核员";
      default:
        return role;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getUserStatusLabel = (status: string) => {
    switch (status) {
      case "active":
        return "在线";
      case "inactive":
        return "离线";
      case "pending":
        return "忙碌";
      default:
        return status;
    }
  };

  const steps = [
    { id: 1, name: "项目信息", description: "填写项目基本信息" },
    { id: 2, name: "上传文档", description: "添加需要审核的文档" },
    { id: 3, name: "配置任务", description: "为每个文档配置审核任务" },
    { id: 4, name: "完成创建", description: "确认信息并创建项目" },
  ];

  const currentPreviewDocument = currentPreviewDoc
    ? documents.find((doc) => doc.id === currentPreviewDoc)
    : null;

  console.log("获取到的用户", users);
  console.log("获取到的可以审核的用户", teamMembers);

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        <PageHeader
          title="创建审核项目"
          description="创建新的文档审核项目"
          badge={{
            text: `步骤 ${currentStep}/4`,
            variant: "outline",
          }}
        />

        <div className="p-6">
          {/* 步骤指示器 */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                      currentStep >= step.id
                        ? "bg-blue-600 border-blue-600 text-white"
                        : "border-gray-300 text-gray-500"
                    }`}
                  >
                    {currentStep > step.id ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </div>
                  <div className="ml-3">
                    <p
                      className={`text-sm font-medium text-left ${
                        currentStep >= step.id
                          ? "text-blue-600"
                          : "text-gray-500"
                      }`}
                    >
                      {step.name}
                    </p>
                    <p className="text-xs text-gray-500">{step.description}</p>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`flex-1 h-0.5 mx-4 ${
                        currentStep > step.id ? "bg-blue-600" : "bg-gray-300"
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="max-w-6xl">
            {/* 步骤1: 项目信息 */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-left">项目基本信息</CardTitle>
                  <CardDescription className="text-left">
                    填写项目的基本信息
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-left pb-2" htmlFor="name">
                      项目名称 *
                    </Label>
                    <Input
                      id="name"
                      value={projectData.name}
                      onChange={(e) =>
                        setProjectData({ ...projectData, name: e.target.value })
                      }
                      placeholder="请输入项目名称"
                      className={!projectData.name ? "border-red-300" : ""}
                    />
                    {!projectData.name && (
                      <p className="text-sm text-red-600 mt-1 text-left">
                        请输入项目名称
                      </p>
                    )}
                  </div>

                  <div>
                    <Label className="text-left pb-2" htmlFor="description">
                      项目描述
                    </Label>
                    <Textarea
                      id="description"
                      value={projectData.description}
                      onChange={(e) =>
                        setProjectData({
                          ...projectData,
                          description: e.target.value,
                        })
                      }
                      placeholder="请描述项目的目标和要求"
                      rows={4}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-left pb-2" htmlFor="priority">
                        项目优先级 *
                      </Label>
                      <Select
                        value={projectData.priority}
                        onValueChange={(value) =>
                          setProjectData({ ...projectData, priority: value })
                        }
                      >
                        <SelectTrigger
                          className={
                            !projectData.priority ? "border-red-300" : ""
                          }
                        >
                          <SelectValue placeholder="选择优先级" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="高">高</SelectItem>
                          <SelectItem value="中">中</SelectItem>
                          <SelectItem value="低">低</SelectItem>
                        </SelectContent>
                      </Select>
                      {!projectData.priority && (
                        <p className="text-sm text-red-600 text-left mt-1">
                          请选择优先级
                        </p>
                      )}
                    </div>

                    <div>
                      <Label className="text-left pb-2" htmlFor="deadline">
                        项目截止日期 *
                      </Label>
                      <Input
                        id="deadline"
                        type="date"
                        value={projectData.deadline}
                        onChange={(e) =>
                          setProjectData({
                            ...projectData,
                            deadline: e.target.value,
                          })
                        }
                        className={
                          !projectData.deadline ? "border-red-300" : ""
                        }
                      />
                      {!projectData.deadline && (
                        <p className="text-sm text-red-600 text-left mt-1">
                          请选择截止日期
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      onClick={() => setCurrentStep(2)}
                      disabled={
                        !projectData.name ||
                        !projectData.priority ||
                        !projectData.deadline
                      }
                    >
                      下一步
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 步骤2: 上传文档 */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="h-5 w-5" />
                      上传文档
                    </CardTitle>
                    <CardDescription>
                      支持 PDF 和 DOCX 格式，可以同时上传多个文档
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div
                      className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                        dragActive
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300 hover:border-gray-400"
                      }`}
                      onDragEnter={handleDrag}
                      onDragLeave={handleDrag}
                      onDragOver={handleDrag}
                      onDrop={handleDrop}
                    >
                      <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <p className="text-lg font-medium text-gray-900 mb-2">
                        拖拽文件到此处或点击上传
                      </p>
                      <p className="text-gray-600 mb-4">
                        支持 PDF 和 DOCX 格式，可以选择多个文件
                      </p>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept=".pdf,.docx"
                        onChange={handleFileChange}
                        className="hidden"
                        multiple
                      />
                      <Button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        选择文件
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* 已上传文档列表 */}
                {documents.length > 0 && (
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-left">
                          已上传文档 ({documents.length})
                        </CardTitle>
                        <Button onClick={handleUploadToAliyun}>
                          <Cloud className="h-5 w-5 mr-2" />
                          一键上传阿里云
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {documents.map((doc) => (
                          <div key={doc.id} className="border rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <FileText className="h-8 w-8 text-blue-500" />
                                <div>
                                  <h4 className="font-medium">
                                    {doc.file.name}
                                  </h4>
                                  <p className="text-sm text-gray-600 text-left">
                                    {(doc.file.size / 1024 / 1024).toFixed(2)}{" "}
                                    MB
                                  </p>
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveDocument(doc.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>

                            {doc.isUploading && (
                              <div className="flex items-center gap-2 text-blue-600">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span className="text-sm">正在上传...</span>
                                <Progress
                                  value={uploadProgress[doc.id] || 0}
                                  className="flex-1"
                                />
                              </div>
                            )}

                            {doc.isParsing && (
                              <div className="flex items-center gap-2 text-green-600">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span className="text-sm">正在解析文档...</span>
                                <Progress value={45} className="flex-1" />
                              </div>
                            )}

                            {doc.parsed && (
                              <div className="flex items-center gap-2 text-green-600">
                                <CheckCircle className="h-4 w-4" />
                                <span className="text-sm">
                                  解析完成 - {doc.metadata.pageCount} 页，
                                  {doc.metadata.wordCount} 字
                                </span>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setCurrentPreviewDoc(doc.id)}
                                >
                                  <Eye className="mr-1 h-3 w-3" />
                                  预览
                                </Button>
                              </div>
                            )}

                            {doc.fileUrl && (
                              <div className="flex items-center gap-2 text-blue-600">
                                <span className="text-sm">
                                  文件地址: {doc.fileUrl}
                                </span>
                              </div>
                            )}

                            {doc.fileId && (
                              <div className="flex items-center gap-2 text-blue-600">
                                <span className="text-sm">
                                  文件ID: {doc.fileId}
                                </span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep(1)}>
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    上一步
                  </Button>
                  <Button
                    onClick={() => setCurrentStep(3)}
                    disabled={documents.length === 0}
                  >
                    下一步
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* 步骤3: 配置任务 */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-left">任务配置</CardTitle>
                    <CardDescription className="text-left">
                      为每个文档配置审核任务信息
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {documents.map((doc, index) => (
                        <div key={doc.id} className="border rounded-lg p-6">
                          <div className="flex items-center gap-3 mb-4">
                            <FileText className="h-6 w-6 text-blue-500" />
                            <div>
                              <h4 className="font-medium text-lg">
                                {doc.file.name}
                              </h4>
                              <p className="text-sm text-gray-600 text-left">
                                {doc.metadata.pageCount} 页 ·{" "}
                                {doc.metadata.wordCount} 字
                              </p>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label
                                className="text-left pb-2"
                                htmlFor={`title-${doc.id}`}
                              >
                                任务标题 *
                              </Label>
                              <Input
                                id={`title-${doc.id}`}
                                value={doc.title}
                                onChange={(e) =>
                                  handleDocumentUpdate(
                                    doc.id,
                                    "title",
                                    e.target.value
                                  )
                                }
                                placeholder="请输入任务标题"
                                className={!doc.title ? "border-red-300" : ""}
                              />
                            </div>

                            <div>
                              <Label
                                className="text-left pb-2"
                                htmlFor={`priority-${doc.id}`}
                              >
                                优先级 *
                              </Label>
                              <Select
                                value={doc.priority}
                                onValueChange={(value) =>
                                  handleDocumentUpdate(
                                    doc.id,
                                    "priority",
                                    value
                                  )
                                }
                              >
                                <SelectTrigger
                                  className={
                                    !doc.priority ? "border-red-300" : ""
                                  }
                                >
                                  <SelectValue placeholder="选择优先级" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="高">高</SelectItem>
                                  <SelectItem value="中">中</SelectItem>
                                  <SelectItem value="低">低</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label
                                className="text-left pb-2"
                                htmlFor={`deadline-${doc.id}`}
                              >
                                截止日期 *
                              </Label>
                              <Input
                                id={`deadline-${doc.id}`}
                                type="date"
                                value={doc.deadline}
                                onChange={(e) =>
                                  handleDocumentUpdate(
                                    doc.id,
                                    "deadline",
                                    e.target.value
                                  )
                                }
                                className={
                                  !doc.deadline ? "border-red-300" : ""
                                }
                              />
                            </div>

                            <div>
                              <Label
                                className="text-left pb-2"
                                htmlFor={`assignee-${doc.id}`}
                              >
                                分配审核员 *
                              </Label>
                              <Select
                                value={doc.assignee}
                                onValueChange={(value) =>
                                  handleDocumentUpdate(
                                    doc.id,
                                    "assignee",
                                    value
                                  )
                                }
                              >
                                <SelectTrigger
                                  className={
                                    !doc.assignee ? "border-red-300" : ""
                                  }
                                >
                                  <SelectValue placeholder="选择审核员">
                                    {doc.assignee && (
                                      <div className="flex items-center gap-2">
                                        {(() => {
                                          const selectedMember =
                                            teamMembers.find(
                                              (member) =>
                                                member.name === doc.assignee
                                            );
                                          if (!selectedMember) return null;
                                          return (
                                            <>
                                              <img
                                                src={
                                                  selectedMember.avatar ||
                                                  "/placeholder.svg"
                                                }
                                                alt={selectedMember.name}
                                                className="w-5 h-5 rounded-full object-cover"
                                              />
                                              <span>{selectedMember.name}</span>
                                            </>
                                          );
                                        })()}
                                      </div>
                                    )}
                                  </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                  {teamMembers.map((member) => (
                                    <SelectItem
                                      key={member.id}
                                      value={member.name}
                                    >
                                      <div className="flex items-center gap-3 py-1">
                                        <img
                                          src={
                                            member.avatar || "/placeholder.svg"
                                          }
                                          alt={member.name}
                                          className="w-6 h-6 rounded-full object-cover"
                                        />
                                        <div className="flex-1">
                                          <div className="flex items-center gap-2">
                                            <span className="font-medium text-sm">
                                              {member.name}
                                            </span>
                                            <Badge
                                              className={getStatusColor(
                                                member.status
                                              )}
                                              variant="outline"
                                            >
                                              {getUserStatusLabel(
                                                member.status
                                              )}
                                            </Badge>
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {getRoleLabel(
                                              member.roles?.[0] || "ROLE_MEMBER"
                                            )}{" "}
                                            · {member.workload}个任务
                                          </div>
                                        </div>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label
                                className="text-left pb-2"
                                htmlFor={`template-${doc.model}`}
                              >
                                模型选择 *
                              </Label>
                              <Select
                                value={doc.model}
                                onValueChange={(value) =>
                                  handleDocumentUpdate(doc.id, "model", value)
                                }
                              >
                                <SelectTrigger
                                  className={!doc.model ? "border-red-300" : ""}
                                >
                                  <SelectValue placeholder="选择模型" />
                                </SelectTrigger>
                                <SelectContent>
                                  {models.map((model) => (
                                    <SelectItem
                                      key={model.modelId}
                                      value={model.modelId}
                                    >
                                      {model.modelId}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="col-span-2">
                              <Label
                                className="text-left pb-2"
                                htmlFor={`template-${doc.prompt}`}
                              >
                                提示词选择 *
                              </Label>
                              <Select
                                value={doc.prompt}
                                onValueChange={(value) =>
                                  handleDocumentUpdate(doc.id, "prompt", value)
                                }
                              >
                                <SelectTrigger
                                  className={
                                    !doc.prompt ? "border-red-300" : ""
                                  }
                                >
                                  <SelectValue placeholder="选择模型" />
                                </SelectTrigger>
                                <SelectContent>
                                  {prompts.map((prompt) => (
                                    <SelectItem
                                      className="w-180 truncate"
                                      key={prompt.promptId}
                                      value={prompt.promptId}
                                    >
                                      <div className="flex items-center gap-2">
                                        <Badge variant="outline">
                                          {prompt.type}
                                        </Badge>
                                        <span className="w-180 truncate">
                                          {prompt.text}
                                        </span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep(2)}>
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    上一步
                  </Button>
                  <Button
                    onClick={() => setCurrentStep(4)}
                    disabled={documents.some(
                      (doc) =>
                        !doc.title ||
                        !doc.assignee ||
                        !doc.priority ||
                        !doc.deadline ||
                        !doc.model ||
                        !doc.prompt
                    )}
                  >
                    下一步
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* 步骤4: 确认创建 */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-left">确认项目信息</CardTitle>
                    <CardDescription className="text-left">
                      请确认以下信息无误后创建项目
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* 项目基本信息 */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3 text-left">
                          项目信息
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div className="flex gap-2">
                            <span className="text-gray-600 text-left">
                              项目名称:
                            </span>
                            <span className="font-medium">
                              {projectData.name}
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <span className="text-gray-600 text-left">
                              优先级:
                            </span>
                            <Badge>{projectData.priority}</Badge>
                          </div>
                          <div className="flex gap-2">
                            <span className="text-gray-600 text-left">
                              截止日期:
                            </span>
                            <span className="font-medium">
                              {projectData.deadline}
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <span className="text-gray-600 text-left">
                              文档数量:
                            </span>
                            <span className="font-medium">
                              {documents.length} 个
                            </span>
                          </div>
                          {projectData.description && (
                            <div className="md:col-span-2 text-left">
                              <span className="text-gray-600">项目描述:</span>
                              <p className="mt-1 text-gray-900">
                                {projectData.description}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 任务列表 */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3 text-left">
                          任务列表
                        </h4>
                        <div className="space-y-3">
                          {documents.map((doc, index) => (
                            <div key={doc.id} className="border rounded-lg p-4">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h5 className="font-medium text-gray-900 mb-2 text-left">
                                    {doc.title}
                                  </h5>
                                  <p className="text-sm text-gray-600 mb-4 text-left">
                                    {doc.file.name}
                                  </p>
                                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                                    <div className="text-left">
                                      <span className="text-gray-500">
                                        负责人:
                                      </span>
                                      <span className="ml-1 font-medium">
                                        {doc.assignee}
                                      </span>
                                    </div>
                                    <div className="text-left">
                                      <span className="text-gray-500">
                                        优先级:
                                      </span>
                                      <Badge className="ml-1" variant="outline">
                                        {doc.priority}
                                      </Badge>
                                    </div>
                                    <div className="text-left">
                                      <span className="text-gray-500">
                                        截止:
                                      </span>
                                      <span className="ml-1 font-medium">
                                        {doc.deadline}
                                      </span>
                                    </div>
                                    <div className="text-left">
                                      <span className="text-gray-500">
                                        模型:
                                      </span>
                                      <span className="ml-1 font-medium">
                                        {doc.model}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                <div className="text-right text-sm text-gray-500">
                                  <div>{doc.metadata.pageCount} 页</div>
                                  <div>{doc.metadata.wordCount} 字</div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep(3)}>
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    上一步
                  </Button>
                  <Button onClick={handleSubmit}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    创建项目
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* 文档预览侧边栏 */}
          {currentPreviewDocument && (
            <div className="fixed inset-y-0 right-0 w-1/2 bg-white border-l shadow-lg z-50 overflow-hidden">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between p-4 border-b">
                  <h3 className="font-medium">文档预览</h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setCurrentPreviewDoc(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex-1 overflow-y-auto p-4">
                  <div className="mb-4">
                    <h4 className="font-medium mb-2">
                      {currentPreviewDocument.file.name}
                    </h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>
                        第 {currentPage} 页，共{" "}
                        {currentPreviewDocument.metadata.pageCount} 页
                      </span>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setCurrentPage(Math.max(1, currentPage - 1))
                          }
                          disabled={currentPage === 1}
                        >
                          <ChevronLeft className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setCurrentPage(
                              Math.min(
                                currentPreviewDocument.metadata.pageCount,
                                currentPage + 1
                              )
                            )
                          }
                          disabled={
                            currentPage ===
                            currentPreviewDocument.metadata.pageCount
                          }
                        >
                          <ChevronRight className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white border rounded-lg p-4">
                    {/* <MarkdownRenderer
                      content={currentPreviewDocument.pages[currentPage - 1] || ""}
                      className="markdown-content"
                    /> */}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectCreate;
