import type React from "react"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import PageHeader from "@/components/PageHeader"
import {
  Settings,
  Bell,
  Shield,
  Database,
  Mail,
  FileText,
  Save,
  RefreshCw,
  Download,
  Upload,
  AlertTriangle,
} from "lucide-react"

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    // 系统设置
    systemName: "文字审核系统",
    systemDescription: "专业的文档审核管理平台",
    maxFileSize: "100",
    allowedFileTypes: ["pdf", "docx"],
    sessionTimeout: "30",

    // 通知设置
    emailNotifications: true,
    taskAssignmentNotification: true,
    taskCompletionNotification: true,
    systemMaintenanceNotification: false,

    // 安全设置
    passwordMinLength: "8",
    passwordRequireSpecialChar: true,
    loginAttemptLimit: "5",
    accountLockoutDuration: "15",
    twoFactorAuth: false,

    // 审核设置
    defaultPriority: "中",
    autoAssignment: false,
    reviewDeadlineDefault: "7",
    qualityCheckRequired: true,

    // 邮件设置
    smtpServer: "smtp.company.com",
    smtpPort: "587",
    smtpUsername: "<EMAIL>",
    smtpPassword: "",
    emailFrom: "文字审核系统 <<EMAIL>>",
  })

  const [isSaving, setIsSaving] = useState(false)

  const currentUser = {
    role: "admin",
    permissions: ["system_settings", "manage_users", "view_all_tasks"],
  }

  const canManageSettings = currentUser.permissions.includes("system_settings")

  const handleSave = async () => {
    setIsSaving(true)
    // 模拟保存设置
    setTimeout(() => {
      setIsSaving(false)
      alert("设置已保存")
    }, 1000)
  }

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = "system-settings.json"
    link.click()
  }

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string)
          setSettings({ ...settings, ...importedSettings })
          alert("设置已导入")
        } catch (error) {
          alert("导入失败：文件格式错误")
        }
      }
      reader.readAsText(file)
    }
  }

  const handleResetSettings = () => {
    if (confirm("确定要重置所有设置为默认值吗？此操作不可撤销。")) {
      // 重置为默认设置
      console.log("重置设置")
    }
  }

  if (!canManageSettings) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1">
          <PageHeader title="系统设置" description="配置系统参数和功能选项" />
          <div className="p-6">
            <div className="text-center py-12">
              <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">访问受限</h2>
              <p className="text-gray-600">您没有权限访问系统设置</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        <PageHeader
          title="系统设置"
          description="配置系统参数和功能选项"
          actions={
            <>
              <input
                type="file"
                accept=".json"
                onChange={handleImportSettings}
                className="hidden"
                id="import-settings"
              />
              <label htmlFor="import-settings">
                <Button variant="outline" asChild>
                  <span>
                    <Upload className="mr-2 h-4 w-4" />
                    导入设置
                  </span>
                </Button>
              </label>
              <Button variant="outline" onClick={handleExportSettings}>
                <Download className="mr-2 h-4 w-4" />
                导出设置
              </Button>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                保存设置
              </Button>
            </>
          }
        />

        <div className="p-6">
          <Tabs defaultValue="system" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="system" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                系统设置
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                通知设置
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                安全设置
              </TabsTrigger>
              <TabsTrigger value="review" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                审核设置
              </TabsTrigger>
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                邮件设置
              </TabsTrigger>
            </TabsList>

            {/* 系统设置 */}
            <TabsContent value="system" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                  <CardDescription>配置系统的基本信息和显示名称</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="systemName">系统名称</Label>
                    <Input
                      id="systemName"
                      value={settings.systemName}
                      onChange={(e) => setSettings({ ...settings, systemName: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="systemDescription">系统描述</Label>
                    <Textarea
                      id="systemDescription"
                      value={settings.systemDescription}
                      onChange={(e) => setSettings({ ...settings, systemDescription: e.target.value })}
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>文件上传设置</CardTitle>
                  <CardDescription>配置文件上传的限制和支持的格式</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="maxFileSize">最大文件大小 (MB)</Label>
                    <Input
                      id="maxFileSize"
                      type="number"
                      value={settings.maxFileSize}
                      onChange={(e) => setSettings({ ...settings, maxFileSize: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label>支持的文件类型</Label>
                    <div className="flex gap-2 mt-2">
                      {["pdf", "docx", "txt", "doc"].map((type) => (
                        <Badge
                          key={type}
                          variant={settings.allowedFileTypes.includes(type) ? "default" : "outline"}
                          className="cursor-pointer"
                          onClick={() => {
                            const newTypes = settings.allowedFileTypes.includes(type)
                              ? settings.allowedFileTypes.filter((t) => t !== type)
                              : [...settings.allowedFileTypes, type]
                            setSettings({ ...settings, allowedFileTypes: newTypes })
                          }}
                        >
                          {type.toUpperCase()}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="sessionTimeout">会话超时时间 (分钟)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={settings.sessionTimeout}
                      onChange={(e) => setSettings({ ...settings, sessionTimeout: e.target.value })}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 通知设置 */}
            <TabsContent value="notifications" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>通知偏好</CardTitle>
                  <CardDescription>配置系统通知的发送规则</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="emailNotifications">邮件通知</Label>
                      <p className="text-sm text-gray-600">启用邮件通知功能</p>
                    </div>
                    <Switch
                      id="emailNotifications"
                      checked={settings.emailNotifications}
                      onCheckedChange={(checked) => setSettings({ ...settings, emailNotifications: checked })}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="taskAssignmentNotification">任务分配通知</Label>
                      <p className="text-sm text-gray-600">当任务被分配时发送通知</p>
                    </div>
                    <Switch
                      id="taskAssignmentNotification"
                      checked={settings.taskAssignmentNotification}
                      onCheckedChange={(checked) => setSettings({ ...settings, taskAssignmentNotification: checked })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="taskCompletionNotification">任务完成通知</Label>
                      <p className="text-sm text-gray-600">当任务完成时发送通知</p>
                    </div>
                    <Switch
                      id="taskCompletionNotification"
                      checked={settings.taskCompletionNotification}
                      onCheckedChange={(checked) => setSettings({ ...settings, taskCompletionNotification: checked })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="systemMaintenanceNotification">系统维护通知</Label>
                      <p className="text-sm text-gray-600">系统维护时发送通知</p>
                    </div>
                    <Switch
                      id="systemMaintenanceNotification"
                      checked={settings.systemMaintenanceNotification}
                      onCheckedChange={(checked) =>
                        setSettings({ ...settings, systemMaintenanceNotification: checked })
                      }
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 安全设置 */}
            <TabsContent value="security" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>密码策略</CardTitle>
                  <CardDescription>配置用户密码的安全要求</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="passwordMinLength">密码最小长度</Label>
                    <Input
                      id="passwordMinLength"
                      type="number"
                      min="6"
                      max="20"
                      value={settings.passwordMinLength}
                      onChange={(e) => setSettings({ ...settings, passwordMinLength: e.target.value })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="passwordRequireSpecialChar">要求特殊字符</Label>
                      <p className="text-sm text-gray-600">密码必须包含特殊字符</p>
                    </div>
                    <Switch
                      id="passwordRequireSpecialChar"
                      checked={settings.passwordRequireSpecialChar}
                      onCheckedChange={(checked) => setSettings({ ...settings, passwordRequireSpecialChar: checked })}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>登录安全</CardTitle>
                  <CardDescription>配置登录相关的安全策略</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="loginAttemptLimit">登录尝试次数限制</Label>
                    <Input
                      id="loginAttemptLimit"
                      type="number"
                      min="3"
                      max="10"
                      value={settings.loginAttemptLimit}
                      onChange={(e) => setSettings({ ...settings, loginAttemptLimit: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="accountLockoutDuration">账户锁定时长 (分钟)</Label>
                    <Input
                      id="accountLockoutDuration"
                      type="number"
                      min="5"
                      max="60"
                      value={settings.accountLockoutDuration}
                      onChange={(e) => setSettings({ ...settings, accountLockoutDuration: e.target.value })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="twoFactorAuth">双因素认证</Label>
                      <p className="text-sm text-gray-600">启用双因素认证增强安全性</p>
                    </div>
                    <Switch
                      id="twoFactorAuth"
                      checked={settings.twoFactorAuth}
                      onCheckedChange={(checked) => setSettings({ ...settings, twoFactorAuth: checked })}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 审核设置 */}
            <TabsContent value="review" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>审核流程</CardTitle>
                  <CardDescription>配置文档审核的默认设置</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="defaultPriority">默认优先级</Label>
                    <Select
                      value={settings.defaultPriority}
                      onValueChange={(value) => setSettings({ ...settings, defaultPriority: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="低">低</SelectItem>
                        <SelectItem value="中">中</SelectItem>
                        <SelectItem value="高">高</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="reviewDeadlineDefault">默认审核期限 (天)</Label>
                    <Input
                      id="reviewDeadlineDefault"
                      type="number"
                      min="1"
                      max="30"
                      value={settings.reviewDeadlineDefault}
                      onChange={(e) => setSettings({ ...settings, reviewDeadlineDefault: e.target.value })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="autoAssignment">自动分配任务</Label>
                      <p className="text-sm text-gray-600">根据负载自动分配审核任务</p>
                    </div>
                    <Switch
                      id="autoAssignment"
                      checked={settings.autoAssignment}
                      onCheckedChange={(checked) => setSettings({ ...settings, autoAssignment: checked })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="qualityCheckRequired">质量检查</Label>
                      <p className="text-sm text-gray-600">完成审核后需要质量检查</p>
                    </div>
                    <Switch
                      id="qualityCheckRequired"
                      checked={settings.qualityCheckRequired}
                      onCheckedChange={(checked) => setSettings({ ...settings, qualityCheckRequired: checked })}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 邮件设置 */}
            <TabsContent value="email" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>SMTP 配置</CardTitle>
                  <CardDescription>配置邮件服务器设置</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="smtpServer">SMTP 服务器</Label>
                      <Input
                        id="smtpServer"
                        value={settings.smtpServer}
                        onChange={(e) => setSettings({ ...settings, smtpServer: e.target.value })}
                        placeholder="smtp.example.com"
                      />
                    </div>
                    <div>
                      <Label htmlFor="smtpPort">端口</Label>
                      <Input
                        id="smtpPort"
                        value={settings.smtpPort}
                        onChange={(e) => setSettings({ ...settings, smtpPort: e.target.value })}
                        placeholder="587"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="smtpUsername">用户名</Label>
                    <Input
                      id="smtpUsername"
                      value={settings.smtpUsername}
                      onChange={(e) => setSettings({ ...settings, smtpUsername: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="smtpPassword">密码</Label>
                    <Input
                      id="smtpPassword"
                      type="password"
                      value={settings.smtpPassword}
                      onChange={(e) => setSettings({ ...settings, smtpPassword: e.target.value })}
                      placeholder="••••••••"
                    />
                  </div>
                  <div>
                    <Label htmlFor="emailFrom">发件人</Label>
                    <Input
                      id="emailFrom"
                      value={settings.emailFrom}
                      onChange={(e) => setSettings({ ...settings, emailFrom: e.target.value })}
                      placeholder="系统名称 <<EMAIL>>"
                    />
                  </div>
                  <div className="pt-4">
                    <Button variant="outline">
                      <Mail className="mr-2 h-4 w-4" />
                      发送测试邮件
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* 危险操作区域 */}
          <Card className="border-red-200 bg-red-50 mt-6">
            <CardHeader>
              <CardTitle className="text-red-800 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                危险操作
              </CardTitle>
              <CardDescription className="text-red-600">以下操作可能会影响系统正常运行，请谨慎操作</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button
                  variant="outline"
                  onClick={handleResetSettings}
                  className="border-red-300 text-red-700 hover:bg-red-100"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重置所有设置
                </Button>
                <Button variant="outline" className="border-red-300 text-red-700 hover:bg-red-100">
                  <Database className="mr-2 h-4 w-4" />
                  清空系统数据
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage;