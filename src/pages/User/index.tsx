import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import PageHeader from "@/components/PageHeader";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Shield,
  UserCheck,
  UserX,
  MoreHorizontal,
  User,
  RefreshCw,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  useUserStore,
  type Permission,
  type UserRole,
} from "@/store/userStore";
import { useAuthStore } from "@/store/authStore";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { UserManagementButton } from "@/components/PermissionButton";
import { Avatar, AvatarImage, AvatarFallback } from "@radix-ui/react-avatar";

const UserPage: React.FC = () => {
  const {
    users,
    roles,
    filters,
    loading,
    error,
    fetchUsers,
    setFilters,
    addUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
  } = useUserStore();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);

  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "",
    department: "",
    avatar: "",
    permissions: [] as Permission[],
  });

  // 获取当前用户信息和权限
  const userInfo = useAuthStore((state) => state.userInfo);
  const userPermissions = userInfo?.permissions || [];

  // 当前用户信息
  const currentUser = {
    id: userInfo?.userId || "1",
    roles: userInfo?.roles || ["ROLE_MEMBER"],
    permissions: userPermissions,
  };

  // 首次加载时获取用户列表
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // 筛选用户
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      (user.name?.toLowerCase() || "").includes(
        filters.searchTerm.toLowerCase()
      ) ||
      (user.email?.toLowerCase() || "").includes(
        filters.searchTerm.toLowerCase()
      );
    const matchesRole =
      filters.roleFilter === "all" ||
      user.roles?.includes(filters.roleFilter as UserRole);
    const matchesStatus =
      filters.statusFilter === "all" || user.status === filters.statusFilter;

    return matchesSearch && matchesRole && matchesStatus;
  });

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ searchTerm: e.target.value });
  };

  const handleRoleFilterChange = (value: string) => {
    setFilters({ roleFilter: value });
  };

  const handleStatusFilterChange = (value: string) => {
    setFilters({ statusFilter: value });
  };

  const handleRefresh = () => {
    fetchUsers();
  };

  const handleCreateUser = async () => {
    const success = await addUser({
      name: newUser.name,
      email: newUser.email,
      status: "active",
      lastLogin: new Date().toLocaleString(),
      avatar: newUser.avatar,
      permissions: newUser.permissions,
    });

    if (success) {
      setIsCreateDialogOpen(false);
      setNewUser({
        name: "",
        email: "",
        role: "",
        department: "",
        avatar: "",
        permissions: [],
      });
    }
  };

  const handleEditUser = (user: any) => {
    setSelectedUser(user);
    setIsEditDialogOpen(true);
  };

  const handleDeleteUser = async (userId: string) => {
    if (confirm("确定要删除这个用户吗？")) {
      await deleteUser(userId);
    }
  };

  const handleToggleStatus = async (userId: string) => {
    await toggleUserStatus(userId);
  };

  const handleRoleChange = (role: string) => {
    const selectedRole = roles.find((r) => r.value === role);
    if (selectedRole) {
      setNewUser({
        ...newUser,
        role,
        permissions: selectedRole.permissions,
      });
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case "ROLE_ADMIN":
        return "bg-red-100 text-red-800";
      case "ROLE_LEADER":
        return "bg-purple-100 text-purple-800";
      case "ROLE_MEMBER":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case "ROLE_ADMIN":
        return "管理员";
      case "ROLE_LEADER":
        return "组长";
      case "ROLE_MEMBER":
        return "组员";
      default:
        return role;
    }
  };

  // 检查当前用户是否有权限执行操作
  const canManageUsers = currentUser.permissions.includes("manage_users");

  // 所有权限列表
  const allPermissions = [
    {
      value: "manage_users" as Permission,
      label: "用户管理",
      description: "创建、编辑、删除用户",
    },
    {
      value: "create_task" as Permission,
      label: "创建任务",
      description: "创建新的审核任务",
    },
    {
      value: "assign_task" as Permission,
      label: "分配任务",
      description: "将任务分配给其他用户",
    },
    {
      value: "review_documents" as Permission,
      label: "文档审核",
      description: "执行文档审核工作",
    },
    {
      value: "view_all_tasks" as Permission,
      label: "查看所有任务",
      description: "查看系统中的所有任务",
    },
    {
      value: "view_task" as Permission,
      label: "查看团队任务",
      description: "查看团队内的任务",
    },
    {
      value: "view_task" as Permission,
      label: "查看个人任务",
      description: "查看分配给自己的任务",
    },
    {
      value: "system_settings" as Permission,
      label: "系统设置",
      description: "修改系统配置",
    },
    {
      value: "manage_team" as Permission,
      label: "团队管理",
      description: "管理团队成员",
    },
  ];

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1">
        <PageHeader
          title="用户管理"
          description="管理系统用户和权限"
          actions={
            <Dialog
              open={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
            >
              <DialogTrigger asChild>
                <UserManagementButton>
                  <Plus className="mr-2 h-4 w-4" />
                  添加用户
                </UserManagementButton>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>添加新用户</DialogTitle>
                  <DialogDescription>创建新的系统用户账户</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">姓名</Label>
                      <Input
                        id="name"
                        value={newUser.name}
                        onChange={(e) =>
                          setNewUser({ ...newUser, name: e.target.value })
                        }
                        placeholder="请输入姓名"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">邮箱</Label>
                      <Input
                        id="email"
                        type="email"
                        value={newUser.email}
                        onChange={(e) =>
                          setNewUser({ ...newUser, email: e.target.value })
                        }
                        placeholder="请输入邮箱"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="role">角色</Label>
                      <Select
                        value={newUser.role}
                        onValueChange={handleRoleChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择角色" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role.value} value={role.value}>
                              <div>
                                <div className="font-medium">{role.label}</div>
                                <div className="text-xs text-gray-500">
                                  {role.description}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="department">部门</Label>
                      <Input
                        id="department"
                        value={newUser.department}
                        onChange={(e) =>
                          setNewUser({
                            ...newUser,
                            department: e.target.value,
                          })
                        }
                        placeholder="请输入部门"
                      />
                    </div>
                  </div>
                  <div>
                    <Label>权限列表</Label>
                    <div className="mt-2 space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
                      {newUser.permissions.map((permission) => {
                        const permissionInfo = allPermissions.find(
                          (p) => p.value === permission
                        );
                        return (
                          <div
                            key={permission}
                            className="flex items-center gap-2"
                          >
                            <Shield className="h-4 w-4 text-green-500" />
                            <span className="text-sm font-medium">
                              {permissionInfo?.label}
                            </span>
                            <span className="text-xs text-gray-500">
                              - {permissionInfo?.description}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="avatar">头像</Label>
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        {newUser.avatar ? (
                          <img
                            src={newUser.avatar || "/placeholder.svg"}
                            alt="头像预览"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User className="w-8 h-8 text-gray-400" />
                        )}
                      </div>
                      <div>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const reader = new FileReader();
                              reader.onload = (e) => {
                                setNewUser({
                                  ...newUser,
                                  avatar: e.target?.result as string,
                                });
                              };
                              reader.readAsDataURL(file);
                            }
                          }}
                          className="hidden"
                          id="avatar-upload"
                        />
                        <label htmlFor="avatar-upload">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <span>选择头像</span>
                          </Button>
                        </label>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsCreateDialogOpen(false)}
                    >
                      取消
                    </Button>
                    <Button onClick={handleCreateUser}>创建用户</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          }
        />

        <div className="p-6">
          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>错误</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 搜索和筛选 */}
          <Card className="mb-6">
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="搜索用户姓名、邮箱或部门..."
                      value={filters.searchTerm}
                      onChange={handleSearchChange}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select
                    value={filters.roleFilter}
                    onValueChange={handleRoleFilterChange}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="角色" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部角色</SelectItem>
                      {roles.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          {role.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={filters.statusFilter}
                    onValueChange={handleStatusFilterChange}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="active">激活</SelectItem>
                      <SelectItem value="inactive">停用</SelectItem>
                      <SelectItem value="pending">待激活</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleRefresh}
                    disabled={loading}
                    className="flex-shrink-0"
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
                    />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 用户表格 */}
          <Card>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户信息</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>任务数</TableHead>
                    <TableHead>最后登录</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    // 加载中的骨架屏
                    Array(5)
                      .fill(0)
                      .map((_, index) => (
                        <TableRow key={`skeleton-${index}`}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Skeleton className="w-10 h-10 rounded-full" />
                              <div>
                                <Skeleton className="h-4 w-32 mb-2" />
                                <Skeleton className="h-3 w-40" />
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-6 w-16" />
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-6 w-16" />
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-4 w-8" />
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-6 w-20" />
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-8 w-8 rounded-full" />
                          </TableCell>
                        </TableRow>
                      ))
                  ) : filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={7}
                        className="text-center py-8 text-gray-500"
                      >
                        没有找到匹配的用户
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage
                                src={user.avatar}
                                alt={user.name}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                              <AvatarFallback>
                                {user.name?.charAt(0) || "?"}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium text-gray-900">
                                {user.name}
                              </div>
                              <div className="text-sm text-gray-600">
                                {user.email}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {user.roles?.map((role) => (
                            <Badge
                              key={role}
                              className={getRoleColor(role)}
                              variant="secondary"
                            >
                              {getRoleLabel(role)}
                            </Badge>
                          ))}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(user.status)}>
                            {user.status === "active"
                              ? "激活"
                              : user.status === "inactive"
                              ? "停用"
                              : "待激活"}
                          </Badge>
                        </TableCell>
                        <TableCell>{user.taskCount}</TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {user.lastLogin || "2025-06-21"}
                        </TableCell>
                        <TableCell>
                          {canManageUsers ? (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => handleEditUser(user)}
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑用户
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleToggleStatus(user.userId!)
                                  }
                                >
                                  {user.status === "active" ? (
                                    <>
                                      <UserX className="mr-2 h-4 w-4" />
                                      停用用户
                                    </>
                                  ) : (
                                    <>
                                      <UserCheck className="mr-2 h-4 w-4" />
                                      激活用户
                                    </>
                                  )}
                                </DropdownMenuItem>
                                {user.id !== currentUser.id && (
                                  <DropdownMenuItem
                                    onClick={() => handleDeleteUser(user.id!)}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    删除用户
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          ) : (
                            <span className="text-sm text-gray-400">
                              无权限
                            </span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UserPage;
