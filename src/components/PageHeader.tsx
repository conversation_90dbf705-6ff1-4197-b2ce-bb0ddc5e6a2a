import type React from "react"

import { useLocation } from "react-router-dom"
// import { usePathname } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import BreadcrumbNav from "@/components/BreadcrumbNav"

interface PageHeaderProps {
  title?: string
  description?: string
  actions?: React.ReactNode
  badge?: {
    text: string
    variant?: "default" | "secondary" | "destructive" | "outline"
  }
}

export default function PageHeader({ title, description, actions, badge }: PageHeaderProps) {
  const location = useLocation()
  const pathname = location.pathname

  // 如果是登录页面，不��示页面头部
  if (pathname === "/") {
    return null
  }

  return (
    <div className="bg-white border-b border-gray-200">
      <BreadcrumbNav />
      {(title || description || actions || badge) && (
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {title && (
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                  {badge && <Badge variant={badge.variant || "default"}>{badge.text}</Badge>}
                </div>
              )}
              {description && <p className="text-gray-600 text-left">{description}</p>}
            </div>
            {actions && <div className="flex items-center gap-2">{actions}</div>}
          </div>
        </div>
      )}
    </div>
  )
}
