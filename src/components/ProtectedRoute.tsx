import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuthStore } from "@/store/authStore";
import { useEffect } from "react";

interface ProtectedRouteProps {
  children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuthStore();
  const location = useLocation();

  // 如果未登录，保存当前路径
  useEffect(() => {
    if (!isAuthenticated && !loading) {
      // 保存当前路径到sessionStorage，以便登录后重定向回来
      sessionStorage.setItem(
        "auth_redirect_path",
        location.pathname + location.search
      );
    }
  }, [isAuthenticated, loading, location]);

  // 如果正在加载认证状态，可以显示加载中
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-lg">正在加载...</p>
        </div>
      </div>
    );
  }

  // 如果未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // 如果已登录，渲染子组件或Outlet
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;
