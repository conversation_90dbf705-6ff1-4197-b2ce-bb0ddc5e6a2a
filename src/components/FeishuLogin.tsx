import { useEffect, useRef, useState } from "react"

declare global {
  interface Window {
    QRLogin: any
  }
}

interface FeishuLoginProps {
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}

const FeishuLogin: React.FC<FeishuLoginProps> = ({ onSuccess, onError }) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const qrLoginRef = useRef<any>(null)
  const scriptRef = useRef<HTMLScriptElement | null>(null)
  const [sdkReady, setSdkReady] = useState(false)
  const [listenerAdded, setListenerAdded] = useState(false)
  const [configError, setConfigError] = useState<string | null>(null)
  
  console.log(import.meta.env)
  const clientId = import.meta.env.VITE_FEISHU_APP_ID
  const redirectUri = import.meta.env.VITE_FEISHU_REDIRECT_URI
  const gotoUri = import.meta.env.VITE_FEISHU_GOTO_URI

  // 检查必要的环境变量是否已配置
  useEffect(() => {
    if (!clientId) {
      setConfigError("缺少飞书App ID配置 (VITE_FEISHU_APP_ID)")
      return
    }
    
    if (!redirectUri) {
      setConfigError("缺少重定向URI配置 (VITE_FEISHU_REDIRECT_URI)")
      return
    }
    
    if (!gotoUri) {
      setConfigError("缺少跳转URI配置 (VITE_FEISHU_GOTO_URI)")
      return
    }
    
    setConfigError(null)
  }, [clientId, redirectUri, gotoUri])

  // 处理消息事件的函数
  const handleMessage = (event: MessageEvent) => {
    console.log("收到消息事件:", event.origin)
    
    try {
      // 检查消息是否来自飞书
      if (!qrLoginRef.current) {
        console.log("qrLoginRef.current 不存在")
        return
      }
      
      if (!qrLoginRef.current.matchOrigin) {
        console.log("qrLoginRef.current.matchOrigin 方法不存在")
        return
      }
      
      if (!qrLoginRef.current.matchData) {
        console.log("qrLoginRef.current.matchData 方法不存在")
        return
      }
      
      const originMatch = qrLoginRef.current.matchOrigin(event.origin)
      console.log("origin匹配结果:", originMatch)
      
      const dataMatch = qrLoginRef.current.matchData(event.data)
      console.log("data匹配结果:", dataMatch)
      
      if (originMatch && dataMatch) {
        const loginTmpCode = event.data.tmp_code
        console.log("收到飞书扫码登录临时码:", loginTmpCode)
        
        // 构建跳转URL，使用goto中的URL并添加tmp_code参数
        const goto = `${gotoUri}?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&state=custom_state`
        
        // 跳转到回调页面
        window.location.href = `${goto}&tmp_code=${loginTmpCode}`
        
        if (onSuccess) {
          onSuccess({ code: loginTmpCode })
        }
      }
    } catch (error) {
      console.error("处理消息事件时出错:", error)
      if (onError) onError(error)
    }
  }

  // 添加消息监听器
  useEffect(() => {
    console.log("准备添加消息监听器")
    
    if (listenerAdded) {
      console.log("消息监听器已添加，跳过")
      return
    }
    
    try {
      window.addEventListener('message', handleMessage)
      console.log("消息监听器添加成功")
      setListenerAdded(true)
    } catch (error) {
      console.error("添加消息监听器失败:", error)
      if (onError) onError(error)
    }
    
    return () => {
      try {
        console.log("准备移除消息监听器")
        window.removeEventListener('message', handleMessage)
        console.log("消息监听器移除成功")
        setListenerAdded(false)
      } catch (error) {
        console.error("移除消息监听器失败:", error)
      }
    }
  }, [])

  // 初始化二维码
  useEffect(() => {
    console.log("准备初始化二维码")
    
    // 如果已经初始化过，不再重复执行
    if (qrLoginRef.current) {
      console.log("二维码已初始化，跳过")
      return
    }

    // 检查SDK是否已加载
    const checkAndInitQRCode = () => {
      if (window.QRLogin && containerRef.current) {
        // 确保容器为空
        containerRef.current.innerHTML = ""
        
        try {
          console.log("SDK已加载，开始初始化二维码")
          console.log("clientId:", clientId)
          console.log("redirectUri:", redirectUri)
          console.log("gotoUri:", gotoUri)
      
          const goto = `${gotoUri}?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&state=custom_state`
          console.log("完整goto URL:", goto)

          qrLoginRef.current = new window.QRLogin({
            id: "feishu-login-container",
            goto,
            width: "300",
            height: "300",
            style: {
              margin: "0 auto",
            }
          })
          
          setSdkReady(true)
          console.log("二维码初始化成功")
        } catch (error) {
          console.error("初始化飞书二维码失败:", error)
          if (onError) onError(error)
        }
      } else {
        console.log("SDK或容器不存在，无法初始化二维码")
        console.log("window.QRLogin:", !!window.QRLogin)
        console.log("containerRef.current:", !!containerRef.current)
      }
    }

    // 如果SDK已经加载，直接初始化
    if (window.QRLogin) {
      console.log("SDK已加载，直接初始化")
      checkAndInitQRCode()
    } else {
      console.log("SDK未加载，准备加载脚本")
    }
    
    return () => {
      console.log("准备清理二维码实例")
      // 销毁QRLogin实例
      if (qrLoginRef.current && qrLoginRef.current.destroy) {
        console.log("销毁二维码实例")
        qrLoginRef.current.destroy()
        qrLoginRef.current = null
      }
    }
  }, [clientId, redirectUri, gotoUri, onSuccess, onError])

  return (
    <div className="flex flex-col items-center">
      <div id="feishu-login-container" ref={containerRef} className="w-[300px] h-[300px]"></div>
      <p className="text-sm text-gray-500 mt-4">请使用飞书APP扫码登录</p>
    </div>
  )
} 

export default FeishuLogin;