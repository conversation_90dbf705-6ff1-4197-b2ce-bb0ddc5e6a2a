import { useEffect, useRef } from 'react';
import type { ReactNode } from 'react';
import { useAuthStore } from '@/store/authStore';

interface AuthProviderProps {
  children: ReactNode;
}

const AuthProvider = ({ children }: AuthProviderProps) => {
  // const { isAuthenticated, accessToken, userInfo } = useAuthStore();
  // const refreshTimerRef = useRef<number | null>(null);

  // useEffect(() => {
  //   // 初始化认证状态
  //   console.log('AuthProvider初始化，认证状态:', isAuthenticated);
    
  //   // 如果已登录，设置定时刷新token的逻辑
  //   if (isAuthenticated && accessToken) {
  //     // 可以在这里添加token有效性校验
  //     // 例如，解析JWT查看是否过期
      
  //     // 设置定时刷新token (例如每30分钟刷新一次)
  //     const REFRESH_INTERVAL = 30 * 60 * 1000; // 30分钟
      
  //     const setupRefreshTimer = () => {
  //       // 清除现有的定时器
  //       if (refreshTimerRef.current) {
  //         window.clearTimeout(refreshTimerRef.current);
  //       }
        
  //       // 设置新的定时器
  //       refreshTimerRef.current = window.setTimeout(async () => {
  //         // 这里可以添加刷新token的逻辑
  //         console.log('Token定时刷新...');
          
  //         // 示例: 调用刷新token的API
  //         // const response = await fetch('/api/auth/refresh', {
  //         //   method: 'POST',
  //         //   headers: {
  //         //     'Content-Type': 'application/json',
  //         //     'Authorization': `Bearer ${accessToken}`
  //         //   }
  //         // });
          
  //         // 递归设置下一次刷新
  //         setupRefreshTimer();
  //       }, REFRESH_INTERVAL);
  //     };
      
  //     // 启动定时刷新
  //     setupRefreshTimer();
      
  //     // 组件卸载时清除定时器
  //     return () => {
  //       if (refreshTimerRef.current) {
  //         window.clearTimeout(refreshTimerRef.current);
  //       }
  //     };
  //   }
  // }, [isAuthenticated, accessToken, userInfo]);

  return <>{children}</>;
};

export default AuthProvider; 