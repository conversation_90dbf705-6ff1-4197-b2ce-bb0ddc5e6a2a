import React from "react";
import { Button } from "@/components/ui/button";
import type { Permission } from "@/store/userStore";
import { useHasPermission, useHasAnyPermission } from "@/utils/permissions";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PermissionButtonProps extends React.ComponentProps<"button"> {
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean; // 是否需要所有权限，默认false（任意一个即可）
  fallbackText?: string; // 无权限时的提示文本
  showTooltip?: boolean; // 是否显示权限提示
  hideWhenNoPermission?: boolean; // 无权限时是否隐藏按钮
}

/**
 * 权限按钮组件
 * 根据用户权限决定按钮的可用状态
 */
export const PermissionButton: React.FC<PermissionButtonProps> = ({
  children,
  permission,
  permissions,
  requireAll = false,
  fallbackText = "您没有权限执行此操作",
  showTooltip = true,
  hideWhenNoPermission = false,
  disabled,
  ...buttonProps
}) => {
  // 单个权限检查
  const hasSinglePermission = useHasPermission(permission!);

  // 多个权限检查
  const hasAnyPermissions = useHasAnyPermission(permissions || []);

  // 确定是否有权限
  let hasPermission = false;

  if (permission) {
    // 检查单个权限
    hasPermission = hasSinglePermission;
  } else if (permissions && permissions.length > 0) {
    // 检查多个权限
    if (requireAll) {
      // 需要所有权限
      hasPermission = permissions.every((p) => useHasPermission(p));
    } else {
      // 任意一个权限即可
      hasPermission = hasAnyPermissions;
    }
  } else {
    // 没有指定权限要求，默认允许
    hasPermission = true;
  }

  // 如果无权限且设置为隐藏，则不渲染
  if (!hasPermission && hideWhenNoPermission) {
    return null;
  }

  // 按钮是否禁用
  const isDisabled = disabled || !hasPermission;

  const button = (
    <Button {...buttonProps} disabled={isDisabled}>
      {children}
    </Button>
  );

  // 如果有权限或不显示提示，直接返回按钮
  if (hasPermission || !showTooltip) {
    return button;
  }

  // 无权限时显示提示
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{button}</TooltipTrigger>
        <TooltipContent>
          <p>{fallbackText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// 预定义的权限按钮组件

/**
 * 用户管理按钮
 */
export const UserManagementButton: React.FC<
  Omit<PermissionButtonProps, "permission">
> = (props) => (
  <PermissionButton
    permission="manage_users"
    fallbackText="需要用户管理权限"
    {...props}
  />
);

/**
 * 项目创建按钮
 */
export const ProjectCreateButton: React.FC<
  Omit<PermissionButtonProps, "permission">
> = (props) => (
  <PermissionButton
    permission="create-project"
    fallbackText="需要项目创建权限"
    {...props}
  />
);

/**
 * 任务创建按钮
 */
export const TaskCreateButton: React.FC<
  Omit<PermissionButtonProps, "permission">
> = (props) => (
  <PermissionButton
    permission="create_task"
    fallbackText="需要任务创建权限"
    {...props}
  />
);

/**
 * 任务分配按钮
 */
export const TaskAssignButton: React.FC<
  Omit<PermissionButtonProps, "permission">
> = (props) => (
  <PermissionButton
    permission="assign_task"
    fallbackText="需要任务分配权限"
    {...props}
  />
);

/**
 * 系统设置按钮
 */
export const SystemSettingsButton: React.FC<
  Omit<PermissionButtonProps, "permission">
> = (props) => (
  <PermissionButton
    permission="system_settings"
    fallbackText="需要系统设置权限"
    {...props}
  />
);

/**
 * 管理员专用按钮
 */
export const AdminOnlyButton: React.FC<
  Omit<PermissionButtonProps, "permissions">
> = (props) => (
  <PermissionButton
    permissions={["manage_users", "system_settings"]}
    fallbackText="需要管理员权限"
    {...props}
  />
);

/**
 * 管理员和组长按钮
 */
export const ManagerAndAboveButton: React.FC<
  Omit<PermissionButtonProps, "permissions">
> = (props) => (
  <PermissionButton
    permissions={["manage_users", "manage_team", "system_settings"]}
    fallbackText="需要管理员或组长权限"
    {...props}
  />
);

export default PermissionButton;
