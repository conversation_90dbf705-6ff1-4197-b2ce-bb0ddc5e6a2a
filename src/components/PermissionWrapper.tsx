import React from "react";
import type { Permission } from "@/store/userStore";
import { useHasPermission, useHasAnyPermission } from "@/utils/permissions";

interface PermissionWrapperProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean; // 是否需要所有权限，默认false（任意一个即可）
  fallback?: React.ReactNode; // 无权限时显示的内容
  className?: string;
}

/**
 * 权限包装组件
 * 根据用户权限决定是否渲染子组件
 */
export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permission,
  permissions,
  requireAll = false,
  fallback = null,
  className,
}) => {
  // 单个权限检查
  const hasSinglePermission = useHasPermission(permission!);

  // 多个权限检查
  const hasAnyPermissions = useHasAnyPermission(permissions || []);

  // 确定是否有权限
  let hasPermission = false;

  if (permission) {
    // 检查单个权限
    hasPermission = hasSinglePermission;
  } else if (permissions && permissions.length > 0) {
    // 检查多个权限
    if (requireAll) {
      // 需要所有权限
      hasPermission = permissions.every((p) => useHasPermission(p));
    } else {
      // 任意一个权限即可
      hasPermission = hasAnyPermissions;
    }
  } else {
    // 没有指定权限要求，默认允许
    hasPermission = true;
  }

  if (!hasPermission) {
    return fallback ? <div className={className}>{fallback}</div> : null;
  }

  return <div className={className}>{children}</div>;
};

interface ConditionalRenderProps {
  condition: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * 条件渲染组件
 * 通用的条件渲染工具
 */
export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  condition,
  children,
  fallback = null,
}) => {
  return condition ? <>{children}</> : <>{fallback}</>;
};

// 预定义的权限组件，用于常见场景

/**
 * 管理员专用组件
 */
export const AdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <PermissionWrapper
    permissions={["manage_users", "system_settings"]}
    fallback={fallback}
  >
    {children}
  </PermissionWrapper>
);

/**
 * 管理员和组长可见组件
 */
export const ManagerAndAbove: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <PermissionWrapper
    permissions={["manage_users", "manage_team", "system_settings"]}
    fallback={fallback}
  >
    {children}
  </PermissionWrapper>
);

/**
 * 用户管理权限组件
 */
export const UserManagementOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <PermissionWrapper permission="manage_users" fallback={fallback}>
    {children}
  </PermissionWrapper>
);

/**
 * 项目创建权限组件
 */
export const ProjectCreateOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <PermissionWrapper permission="create-project" fallback={fallback}>
    {children}
  </PermissionWrapper>
);

/**
 * 任务创建权限组件
 */
export const TaskCreateOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <PermissionWrapper permission="create_task" fallback={fallback}>
    {children}
  </PermissionWrapper>
);

/**
 * 系统设置权限组件
 */
export const SystemSettingsOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <PermissionWrapper permission="system_settings" fallback={fallback}>
    {children}
  </PermissionWrapper>
);

export default PermissionWrapper;
