import type React from "react";

import { Link, useLocation } from "react-router-dom";
import {
  Home,
  FileText,
  Users,
  Settings,
  Plus,
  Eye,
  FolderOpen,
  Brain,
  MessageCircle,
} from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface BreadcrumbConfig {
  [key: string]: {
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
    dynamic?: boolean;
  };
}

const breadcrumbConfig: BreadcrumbConfig = {
  // "/": { label: "登录", icon: Home },
  "/dashboard": { label: "工作台", icon: Home },
  "/project": { label: "项目列表", icon: FolderOpen },
  "/project/create": { label: "创建项目", icon: Plus },
  "/task": { label: "任务列表", icon: FileText },
  "/task/create": { label: "创建任务", icon: Plus },
  "/user": { label: "用户管理", icon: Users },
  "/settings": { label: "系统设置", icon: Settings },
  "/ai-model": { label: "AI模型", icon: Brain },
  "/ai-prompt": { label: "提示词管理", icon: MessageCircle },
};

// 动态路由配置
const dynamicRoutes = [
  {
    pattern: /^\/project\/(?!create$)([^\/]+)$/,
    getLabel: (matches: RegExpMatchArray) => `项目详情`,
    icon: FolderOpen,
    parentPath: "/project",
  },
  {
    pattern: /^\/task\/(?!create$)([^\/]+)$/,
    getLabel: (matches: RegExpMatchArray) => `任务详情 #${matches[1]}`,
    icon: Eye,
    parentPath: "/task",
  },
  {
    pattern: /^\/ai-model$/,
    getLabel: (matches: RegExpMatchArray) => `AI模型`,
    icon: Brain,
  },
  {
    pattern: /^\/ai-prompt$/,
    getLabel: (matches: RegExpMatchArray) => `提示词管理`,
    icon: MessageCircle,
  },
];

export default function BreadcrumbNav() {
  const location = useLocation();
  const pathname = location.pathname;

  // 如果是登录页面，不显示面包屑
  if (pathname === "/") {
    return null;
  }

  console.log("Breadcrumb 导航初始化", pathname);

  const generateBreadcrumbs = () => {
    const segments = pathname.split("/").filter(Boolean);
    console.log("Breadcrumb 生成面包屑", segments);

    const breadcrumbs = [];

    // 获取查询参数
    const searchParams = new URLSearchParams(window.location.search);
    console.log("Breadcrumb 生成面包屑 search", searchParams);

    // 检查是否是动态路由
    const dynamicRoute = dynamicRoutes.find((route) =>
      route.pattern.test(pathname)
    );
    console.log("Breadcrumb 动态路由", dynamicRoute);

    if (dynamicRoute) {
      const matches = pathname.match(dynamicRoute.pattern);
      if (matches) {
        // 添加首页（仅当不是当前页面时）
        if (pathname !== "/dashboard") {
          breadcrumbs.push({
            label: "工作台",
            href: "/dashboard",
            icon: Home,
            isLast: false,
          });
        }

        // 处理不同类型的动态路由
        if (pathname.match(/^\/project\/(?!create$)([^\/]+)$/)) {
          console.log("动态路由，项目详情页面");
          // 项目详情页面: 工作台 → 项目列表 → 项目详情
          breadcrumbs.push({
            label: "项目列表",
            href: "/project",
            icon: FolderOpen,
            isLast: false,
          });

          breadcrumbs.push({
            label: "项目详情",
            href: pathname,
            icon: FolderOpen,
            isLast: true,
          });
        } else if (pathname.match(/^\/task\/(?!create$)([^\/]+)$/)) {
          console.log("动态路由，任务详情页面");
          // 任务详情页面: 多种方式判断来源
          const fromProject = location.state?.fromProject;
          const projectId = location.state?.projectId;

          if (fromProject && projectId) {
            // 从项目详情跳转: 工作台 → 项目列表 → 项目详情 → 任务详情
            breadcrumbs.push({
              label: "项目列表",
              href: "/project",
              icon: FolderOpen,
              isLast: false,
            });

            breadcrumbs.push({
              label: "项目详情",
              href: `/project/${projectId}`,
              icon: FolderOpen,
              isLast: false,
            });

            breadcrumbs.push({
              label: `任务详情 #${matches[1]}`,
              href: pathname,
              icon: Eye,
              isLast: true,
            });
          } else {
            // 从任务列表跳转或直接访问: 工作台 → 任务列表 → 任务详情
            breadcrumbs.push({
              label: "任务列表",
              href: "/task",
              icon: FileText,
              isLast: false,
            });

            breadcrumbs.push({
              label: `任务详情 #${matches[1]}`,
              href: pathname,
              icon: Eye,
              isLast: true,
            });
          }
        } else {
          // 其他动态路由的通用处理
          breadcrumbs.push({
            label: dynamicRoute.getLabel(matches),
            href: pathname,
            icon: dynamicRoute.icon,
            isLast: true,
          });
        }
      }
    } else {
      // 处理静态路由
      let currentPath = "";
      let isFirstSegment = true;

      // 特殊处理 /task/create 路由
      if (pathname === "/task/create") {
        // 优先检查查询参数，然后检查 referrer
        const searchParams = new URLSearchParams(window.location.search);
        const projectIdFromQuery = searchParams.get("projectId");
        const referrer = document.referrer;
        const isFromProject = referrer && referrer.includes("/project/");

        // 从 referrer 中提取项目ID
        const projectIdMatch = referrer.match(/\/project\/([^\/\?#]+)/);
        const projectIdFromReferrer = projectIdMatch ? projectIdMatch[1] : "";

        const projectId = projectIdFromQuery || projectIdFromReferrer;

        if (projectId || isFromProject) {
          // 从项目详情创建任务: 工作台 → 项目列表 → 项目详情 → 创建任务
          breadcrumbs.push({
            label: "项目列表",
            href: "/project",
            icon: FolderOpen,
            isLast: false,
          });

          breadcrumbs.push({
            label: "项目详情",
            href: projectId ? `/project/${projectId}` : "/project",
            icon: FolderOpen,
            isLast: false,
          });

          breadcrumbs.push({
            label: "创建任务",
            href: pathname,
            icon: Plus,
            isLast: true,
          });
        } else {
          // 从任务列表创建任务: 工作台 → 任务列表 → 创建任务
          breadcrumbs.push({
            label: "任务列表",
            href: "/task",
            icon: FileText,
            isLast: false,
          });

          breadcrumbs.push({
            label: "创建任务",
            href: pathname,
            icon: Plus,
            isLast: true,
          });
        }
      } else {
        // 其他静态路由的常规处理
        segments.forEach((segment, index) => {
          currentPath += `/${segment}`;
          const config = breadcrumbConfig[currentPath];

          if (config) {
            // 如果是第一个段且不是dashboard页面，先添加dashboard作为首页
            if (isFirstSegment && currentPath !== "/dashboard") {
              breadcrumbs.push({
                label: "工作台",
                href: "/dashboard",
                icon: Home,
                isLast: false,
              });
            }

            breadcrumbs.push({
              label: config.label,
              href: currentPath,
              icon: config.icon,
              isLast: index === segments.length - 1,
            });

            isFirstSegment = false;
          }
        });
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  console.log("Breadcrumb 导航初始化完成", breadcrumbs);

  // 如果没有面包屑，不显示
  if (breadcrumbs.length === 0) {
    return null;
  }

  // 如果只有一个面包屑且不是dashboard页面，不显示
  if (breadcrumbs.length === 1 && pathname !== "/dashboard") {
    return null;
  }

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-3">
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbs.map((crumb) => (
            <div key={crumb.href} className="flex items-center">
              <BreadcrumbItem>
                {crumb.isLast ? (
                  <BreadcrumbPage className="flex items-center gap-2">
                    {crumb.icon && <crumb.icon className="h-4 w-4" />}
                    {crumb.label}
                  </BreadcrumbPage>
                ) : (
                  <Link to={crumb.href}>
                    <BreadcrumbLink className="flex items-center gap-2 hover:text-blue-600">
                      {crumb.icon && <crumb.icon className="h-4 w-4" />}
                      {crumb.label}
                    </BreadcrumbLink>
                  </Link>
                )}
              </BreadcrumbItem>
              {!crumb.isLast && <BreadcrumbSeparator />}
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
