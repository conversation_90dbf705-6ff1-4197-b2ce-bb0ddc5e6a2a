import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuthStore } from "@/store/authStore";
import type { Permission } from "@/store/userStore";
import { PermissionUtils } from "@/utils/permissions";
import { Shield, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PermissionGuardProps {
  requiredPermissions: Permission[];
  requireAll?: boolean; // 是否需要所有权限，默认false（任意一个即可）
  children?: React.ReactNode;
  fallbackPath?: string; // 无权限时重定向的路径
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  requiredPermissions,
  requireAll = false,
  children,
  fallbackPath,
}) => {
  const userInfo = useAuthStore((state) => state.userInfo);
  const userPermissions = userInfo?.permissions || [];

  // 检查用户是否拥有所需权限
  const hasPermission = requireAll
    ? PermissionUtils.hasAllPermissions(userPermissions, requiredPermissions)
    : PermissionUtils.hasAnyPermission(userPermissions, requiredPermissions);

  // 如果指定了重定向路径且用户无权限，则重定向
  if (!hasPermission && fallbackPath) {
    return <Navigate to={fallbackPath} replace />;
  }

  if (!hasPermission) {
    return (
      <div className="flex min-h-screen bg-gray-50">
        <div className="flex-1">
          <div className="p-6">
            <div className="text-center py-12">
              <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                访问受限
              </h2>
              <p className="text-gray-600 mb-6">您没有权限访问此页面</p>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  需要权限: {requiredPermissions.join(", ")}
                </p>
                <p className="text-sm text-gray-500">
                  您的权限:{" "}
                  {userPermissions.length > 0
                    ? userPermissions.join(", ")
                    : "无"}
                </p>
              </div>
              <div className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => window.history.back()}
                  className="mr-2"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  返回上页
                </Button>
                <Button onClick={() => (window.location.href = "/dashboard")}>
                  回到工作台
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return children ? <>{children}</> : <Outlet />;
};

export default PermissionGuard;
