import { <PERSON> } from "react-router-dom";
import { useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  FolderOpen,
  FileText,
  Users,
  Settings,
  Eclipse,
  PanelLeft,
  Brain,
  MessageCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar, // Hook from shadcn's sidebar
} from "@/components/ui/sidebar";
import { NavUser } from "@/components/NavUser";
import { usePermissions } from "@/utils/permissions";
import type { Permission } from "@/store/userStore";

// Define navigation items with required permissions
interface NavigationItem {
  name: string;
  href: string;
  icon: any;
  permissions?: Permission[]; // 需要的权限，为空表示所有用户都可以访问
}

const navigationItems: NavigationItem[] = [
  { name: "工作台", href: "/dashboard", icon: LayoutDashboard }, // 所有用户都可以访问
  {
    name: "项目列表",
    href: "/project",
    icon: FolderOpen,
    permissions: ["view_project"],
  },
  {
    name: "任务列表",
    href: "/task",
    icon: FileText,
    permissions: ["view_task"],
  },
  {
    name: "用户管理",
    href: "/user",
    icon: Users,
    permissions: ["manage_users"],
  },
  {
    name: "大模型管理",
    href: "/ai-model",
    icon: Brain,
    permissions: ["manager_model"],
  },
  {
    name: "提示词管理",
    href: "/ai-prompt",
    icon: MessageCircle,
    permissions: ["manager_prompt"],
  },
  {
    name: "设置",
    href: "/settings",
    icon: Settings,
    permissions: ["system_settings"],
  },
];

export function AppSidebar() {
  const location = useLocation();
  const pathname = location.pathname;
  // useSidebar hook from shadcn's context to control and get sidebar state
  const { state, toggleSidebar } = useSidebar();
  // 获取用户权限
  const { hasAnyPermission } = usePermissions();

  // 过滤用户有权限访问的导航项
  const visibleNavigationItems = navigationItems.filter((item) => {
    // 如果没有权限要求，所有用户都可以看到
    if (!item.permissions || item.permissions.length === 0) {
      return true;
    }
    // 检查用户是否有任意一个所需权限
    return hasAnyPermission(item.permissions);
  });

  return (
    <Sidebar collapsible="icon" className="border-r bg-background">
      <SidebarHeader className="p-4 flex flex-row justify-between items-center h-16">
        {state === "expanded" && (
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                className="data-[slot=sidebar-menu-button]:!p-1.5"
              >
                <Link
                  to="/dashboard"
                  className="focus:outline-none focus-visible:ring-2 focus-visible:ring-ring rounded-sm"
                >
                  <Eclipse className="!size-6" />
                  <span className="text-xl font-semibold">Next 医学</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="hidden lg:flex h-8 w-8 shrink-0"
          aria-label="Toggle Sidebar"
        >
          <PanelLeft className="h-5 w-5" />
        </Button>
      </SidebarHeader>

      {/* Scrollable content area of the sidebar */}
      <SidebarContent className="flex-grow p-2">
        <SidebarMenu>
          {visibleNavigationItems.map((item) => {
            // Determine if the current link is active
            const isActive =
              pathname === item.href ||
              (item.href === "/project" && pathname.startsWith("/project")) ||
              (item.href === "/task" && pathname.startsWith("/task"));

            return (
              <SidebarMenuItem key={item.name}>
                <SidebarMenuButton
                  asChild
                  isActive={isActive}
                  // Tooltip for when sidebar is collapsed (icon-only view)
                  tooltip={{
                    children: item.name,
                    side: "right",
                    align: "center",
                  }}
                >
                  <Link to={item.href} className="w-full">
                    <item.icon className="h-5 w-5 shrink-0" />
                    {/* Text is automatically shown/hidden based on sidebar state */}
                    <span>{item.name}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      {/* Sticky footer section of the sidebar */}
      <SidebarFooter className="p-2 border-t">
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
