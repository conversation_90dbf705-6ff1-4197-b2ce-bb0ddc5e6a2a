# Role-Based Access Control (RBAC) Implementation Guide

## Overview

This document describes the comprehensive Role-Based Access Control (RBAC) system implemented for the document management system. The system provides fine-grained permission control across all application features.

## System Architecture

### 1. User Roles

The system defines three primary user roles:

- **Admin (管理员)**: Full system access with all permissions
- **Manager (组长)**: Project and team management capabilities
- **User (审核员)**: Basic document review and task execution

### 2. Permission System

#### Available Permissions:
- `manage_users` - User management (create, edit, delete users)
- `create-project` - Create new projects
- `create_task` - Create new tasks
- `assign_task` - Assign tasks to users
- `review_documents` - Review and approve documents
- `view_project` - View project details
- `view_all_tasks` - View all tasks in the system
- `view_team_tasks` - View team-specific tasks
- `view_own_tasks` - View personally assigned tasks
- `update_issue` - Update issue status
- `system_settings` - Access system configuration
- `manage_team` - Manage team members

#### Role-Permission Mapping:

**Admin:**
- All permissions (complete system access)

**Manager:**
- `manage_team`, `create_task`, `assign_task`, `view_all_tasks`
- `system_settings`, `create-project`, `view_project`
- `view_team_tasks`, `view_own_tasks`

**User:**
- `review_documents`, `view_all_tasks`, `view_project`
- `update_issue`, `view_own_tasks`

## Implementation Components

### 1. Core Files

#### Authentication Store (`src/store/authStore.ts`)
- Extended `UserInfo` interface to include `role` and `permissions`
- Manages user authentication state and permission data

#### Permission Utilities (`src/utils/permissions.ts`)
- `PermissionUtils` class with permission checking methods
- React hooks for permission validation
- Route-permission mapping configuration

#### Permission Components
- `PermissionGuard` - Route-level protection
- `PermissionWrapper` - Conditional UI rendering
- `PermissionButton` - Permission-aware buttons

### 2. Route Protection

Routes are protected using `PermissionGuard` components in `App.tsx`:

```tsx
<Route 
  path="user" 
  element={
    <PermissionGuard requiredPermissions={['manage_users']}>
      <User />
    </PermissionGuard>
  } 
/>
```

### 3. UI-Level Protection

#### Sidebar Navigation
The `AppSidebar` component filters menu items based on user permissions:
- Only shows navigation items the user has permission to access
- Dynamically updates based on current user role

#### Conditional Rendering
Use permission wrapper components for conditional UI elements:

```tsx
<PermissionWrapper permission="manage_users">
  <Button>Add User</Button>
</PermissionWrapper>
```

#### Permission-Aware Buttons
Use specialized button components:

```tsx
<UserManagementButton>
  Add User
</UserManagementButton>
```

## Development and Testing

### 1. Development Utilities

The system includes development utilities (`src/utils/devUtils.ts`) for testing different user roles:

#### Available in Browser Console:
```javascript
// Switch to different roles
DevPermissionTester.switchToRole("admin")
DevPermissionTester.switchToRole("manager") 
DevPermissionTester.switchToRole("user")

// Check current permissions
DevPermissionTester.printCurrentUserPermissions()

// Test specific permissions
DevPermissionTester.testPermissionCheck("manage_users")

// Test all permissions
DevPermissionTester.testAllPermissions()
```

### 2. Mock Users

Pre-configured mock users for testing:
- **Admin**: Full access to all features
- **Manager**: Project and team management access
- **User**: Basic review and task access

### 3. Testing Scenarios

#### Test Case 1: Admin Access
1. Switch to admin role: `DevPermissionTester.switchToRole("admin")`
2. Verify access to all pages: User Management, Settings, AI Model, etc.
3. Confirm all UI elements are visible and functional

#### Test Case 2: Manager Access
1. Switch to manager role: `DevPermissionTester.switchToRole("manager")`
2. Verify access to: Dashboard, Projects, Tasks, Settings
3. Confirm no access to: User Management
4. Check that project creation and task assignment features are available

#### Test Case 3: User Access
1. Switch to user role: `DevPermissionTester.switchToRole("user")`
2. Verify access to: Dashboard, Projects (view only), Tasks (own tasks)
3. Confirm no access to: User Management, Settings, AI Model
4. Check that only review and update features are available

## API Integration

### 1. HTTP Client Integration

The `httpClient.ts` automatically includes JWT tokens in API requests:
- Retrieves token from localStorage
- Adds `Authorization: Bearer <token>` header
- Handles token refresh and error scenarios

### 2. Backend Permission Validation

**Important**: Frontend permission checks are for UX only. Backend APIs must validate permissions server-side for security.

Recommended backend validation:
```javascript
// Example middleware
function requirePermission(permission) {
  return (req, res, next) => {
    const userPermissions = req.user.permissions;
    if (!userPermissions.includes(permission)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
}
```

## Usage Examples

### 1. Protecting a Page Component

```tsx
import PermissionGuard from '@/components/PermissionGuard';

function MyProtectedPage() {
  return (
    <PermissionGuard requiredPermissions={['manage_users']}>
      <div>Protected content here</div>
    </PermissionGuard>
  );
}
```

### 2. Conditional UI Rendering

```tsx
import { PermissionWrapper } from '@/components/PermissionWrapper';

function MyComponent() {
  return (
    <div>
      <PermissionWrapper permission="create_task">
        <Button>Create Task</Button>
      </PermissionWrapper>
      
      <PermissionWrapper 
        permissions={['manage_users', 'system_settings']}
        fallback={<div>Access Denied</div>}
      >
        <AdminPanel />
      </PermissionWrapper>
    </div>
  );
}
```

### 3. Using Permission Hooks

```tsx
import { usePermissions, useHasPermission } from '@/utils/permissions';

function MyComponent() {
  const { hasPermission, role } = usePermissions();
  const canManageUsers = useHasPermission('manage_users');
  
  return (
    <div>
      <p>Current role: {role}</p>
      {canManageUsers && <UserManagementPanel />}
    </div>
  );
}
```

## Security Considerations

1. **Frontend vs Backend**: Frontend permissions are for UX only - always validate on backend
2. **Token Security**: JWT tokens should have appropriate expiration times
3. **Permission Updates**: Consider how permission changes are propagated to active sessions
4. **Audit Logging**: Log permission-based actions for security auditing

## Troubleshooting

### Common Issues:

1. **User sees "Access Denied"**: Check user's role and permissions in browser console
2. **Navigation items missing**: Verify permission requirements in `AppSidebar.tsx`
3. **Buttons disabled**: Check `PermissionButton` component usage and required permissions
4. **Route access blocked**: Verify `PermissionGuard` configuration in `App.tsx`

### Debug Commands:
```javascript
// Check current user state
DevPermissionTester.printCurrentUserPermissions()

// Test specific permission
DevPermissionTester.testPermissionCheck("permission_name")

// View all available roles
DevPermissionTester.getAvailableRoles()
```

## Future Enhancements

1. **Dynamic Permissions**: Load permissions from backend API
2. **Permission Groups**: Group related permissions for easier management
3. **Temporary Permissions**: Time-limited access grants
4. **Permission Inheritance**: Hierarchical permission structures
5. **Audit Trail**: Track permission usage and changes
