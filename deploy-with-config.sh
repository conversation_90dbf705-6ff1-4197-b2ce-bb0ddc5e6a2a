#!/bin/bash

# 使用配置文件的构建和部署脚本
# 使用方法: ./deploy-with-config.sh [配置文件路径]
# 例如: ./deploy-with-config.sh
# 例如: ./deploy-with-config.sh ./my-deploy.config

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置文件路径
CONFIG_FILE=${1:-"./deploy.config"}

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    log_error "配置文件不存在: $CONFIG_FILE"
    log_error "请创建配置文件或使用示例配置："
    log_error "cp deploy.config.example deploy.config"
    log_error "然后编辑 deploy.config 文件"
    exit 1
fi

# 加载配置文件
log_info "加载配置文件: $CONFIG_FILE"
source "$CONFIG_FILE"

# 检查必需的配置项
if [ -z "$SERVER" ] || [ -z "$USER" ]; then
    log_error "配置文件中缺少必需的配置项 SERVER 或 USER"
    exit 1
fi

# 设置默认值
PORT=${PORT:-22}
REMOTE_PATH=${REMOTE_PATH:-"/home/<USER>"}
LOCAL_DIST_PATH=${LOCAL_DIST_PATH:-"./dist"}
BUILD_COMMAND=${BUILD_COMMAND:-"pnpm build-without-type-check"}
BACKUP_PATH="/home/<USER>"

log_info "部署配置:"
log_info "  服务器: $USER@$SERVER:$PORT"
log_info "  远程路径: $REMOTE_PATH"
log_info "  本地构建目录: $LOCAL_DIST_PATH"
log_info "  构建命令: $BUILD_COMMAND"

# 检查本地依赖
log_info "检查本地环境..."

if ! command -v pnpm &> /dev/null; then
    log_error "pnpm 未安装，请先安装 pnpm"
    exit 1
fi

if ! command -v rsync &> /dev/null; then
    log_error "rsync 未安装，请先安装 rsync"
    exit 1
fi

if ! command -v ssh &> /dev/null; then
    log_error "ssh 未安装，请先安装 ssh"
    exit 1
fi

# 检查是否能连接到服务器
log_info "测试服务器连接..."
log_warning "正在测试SSH连接，如果需要密码请输入..."

# 先尝试无密码连接（密钥认证）
if ssh -p $PORT -o ConnectTimeout=10 -o BatchMode=yes $USER@$SERVER exit 2>/dev/null; then
    log_success "SSH密钥认证连接成功"
    SSH_AUTH_METHOD="key"
else
    # 如果密钥认证失败，尝试密码认证
    log_info "SSH密钥认证失败，尝试密码认证..."
    if ssh -p $PORT -o ConnectTimeout=10 -o PasswordAuthentication=yes $USER@$SERVER exit; then
        log_success "SSH密码认证连接成功"
        SSH_AUTH_METHOD="password"
    else
        log_error "无法连接到服务器 $USER@$SERVER:$PORT"
        log_error "请检查："
        log_error "1. 服务器地址和端口是否正确"
        log_error "2. SSH密钥是否已配置或密码是否正确"
        log_error "3. 网络连接是否正常"
        exit 1
    fi
fi

# 清理旧的构建产物
log_info "清理旧的构建产物..."
if [ -d "$LOCAL_DIST_PATH" ]; then
    rm -rf "$LOCAL_DIST_PATH"
    log_success "已清理旧的构建产物"
fi

# 构建项目
log_info "开始构建项目..."
log_info "执行命令: $BUILD_COMMAND"

if ! eval "$BUILD_COMMAND"; then
    log_error "构建失败"
    exit 1
fi

# 检查构建产物
if [ ! -d "$LOCAL_DIST_PATH" ]; then
    log_error "构建产物目录不存在: $LOCAL_DIST_PATH"
    exit 1
fi

if [ -z "$(ls -A $LOCAL_DIST_PATH)" ]; then
    log_error "构建产物目录为空: $LOCAL_DIST_PATH"
    exit 1
fi

log_success "项目构建完成"
log_info "构建产物位置: $LOCAL_DIST_PATH"

# 显示构建产物信息
log_info "构建产物内容:"
ls -la "$LOCAL_DIST_PATH"

# 备份远程服务器上的旧文件
log_info "备份远程服务器上的旧文件..."
if [ "$SSH_AUTH_METHOD" = "key" ]; then
    ssh -p $PORT -o BatchMode=yes $USER@$SERVER "
        if [ -d '$REMOTE_PATH' ]; then
            echo '备份旧文件到: $BACKUP_PATH'
            cp -r '$REMOTE_PATH' '$BACKUP_PATH'
            echo '备份完成'
        else
            echo '远程目录不存在，将创建新目录'
            mkdir -p '$REMOTE_PATH'
        fi
    "
else
    ssh -p $PORT $USER@$SERVER "
        if [ -d '$REMOTE_PATH' ]; then
            echo '备份旧文件到: $BACKUP_PATH'
            cp -r '$REMOTE_PATH' '$BACKUP_PATH'
            echo '备份完成'
        else
            echo '远程目录不存在，将创建新目录'
            mkdir -p '$REMOTE_PATH'
        fi
    "
fi

# 上传新文件
log_info "上传构建产物到服务器..."
log_info "使用 rsync 同步文件..."

# 使用rsync同步，删除远程多余文件，保持同步
if [ "$SSH_AUTH_METHOD" = "key" ]; then
    RSYNC_SSH_CMD="ssh -p $PORT -o BatchMode=yes"
else
    RSYNC_SSH_CMD="ssh -p $PORT"
fi

if rsync -avz --delete -e "$RSYNC_SSH_CMD" "$LOCAL_DIST_PATH/" "$USER@$SERVER:$REMOTE_PATH/"; then
    log_success "文件上传完成"
else
    log_error "文件上传失败"

    # 如果上传失败，尝试恢复备份
    log_warning "尝试恢复备份..."
    if [ "$SSH_AUTH_METHOD" = "key" ]; then
        ssh -p $PORT -o BatchMode=yes $USER@$SERVER "
            if [ -d '$BACKUP_PATH' ]; then
                rm -rf '$REMOTE_PATH'
                mv '$BACKUP_PATH' '$REMOTE_PATH'
                echo '已恢复备份'
            fi
        "
    else
        ssh -p $PORT $USER@$SERVER "
            if [ -d '$BACKUP_PATH' ]; then
                rm -rf '$REMOTE_PATH'
                mv '$BACKUP_PATH' '$REMOTE_PATH'
                echo '已恢复备份'
            fi
        "
    fi
    exit 1
fi

# 验证部署
log_info "验证部署结果..."
if [ "$SSH_AUTH_METHOD" = "key" ]; then
    ssh -p $PORT -o BatchMode=yes $USER@$SERVER "
        echo '远程目录内容:'
        ls -la '$REMOTE_PATH'
        echo ''
        echo '磁盘使用情况:'
        du -sh '$REMOTE_PATH'
    "
else
    ssh -p $PORT $USER@$SERVER "
        echo '远程目录内容:'
        ls -la '$REMOTE_PATH'
        echo ''
        echo '磁盘使用情况:'
        du -sh '$REMOTE_PATH'
    "
fi

# 清理备份（保留最近3个备份）
log_info "清理旧备份..."
if [ "$SSH_AUTH_METHOD" = "key" ]; then
    ssh -p $PORT -o BatchMode=yes $USER@$SERVER "
        cd /home
        # 保留最近3个备份，删除更旧的
        ls -t www_backup_* 2>/dev/null | tail -n +4 | xargs -r rm -rf
        echo '备份清理完成'
    "
else
    ssh -p $PORT $USER@$SERVER "
        cd /home
        # 保留最近3个备份，删除更旧的
        ls -t www_backup_* 2>/dev/null | tail -n +4 | xargs -r rm -rf
        echo '备份清理完成'
    "
fi

log_success "部署完成！"
log_info "网站已更新到: $USER@$SERVER:$REMOTE_PATH"
log_info "备份位置: $BACKUP_PATH"
