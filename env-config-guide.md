# 环境变量配置指南

在项目根目录下创建以下环境变量文件：

## 开发环境 (.env.local)

```
# 应用基本信息
VITE_APP_NAME=文档管理系统
VITE_APP_BASE_URL=/

# 飞书登录配置
VITE_FEISHU_APP_ID=your_feishu_app_id
VITE_FEISHU_REDIRECT_URI=http://localhost:5174/auth/callback
VITE_FEISHU_GOTO_URI=/dashboard

# API配置
VITE_API_BASE_URL=http://localhost:3000/api
```

## 生产环境 (.env.production)

```
# 应用基本信息
VITE_APP_NAME=文档管理系统
VITE_APP_BASE_URL=/

# 飞书登录配置
VITE_FEISHU_APP_ID=your_feishu_app_id
VITE_FEISHU_REDIRECT_URI=https://your-production-domain.com/auth/callback
VITE_FEISHU_GOTO_URI=/dashboard

# API配置
VITE_API_BASE_URL=https://api.your-production-domain.com/api
```

## 使用环境变量

在代码中可以通过 `import.meta.env` 访问环境变量：

```typescript
// 例如：
const appName = import.meta.env.VITE_APP_NAME;
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
```

## 注意事项

1. 所有环境变量必须以 `VITE_` 开头才能在客户端代码中访问
2. `.env.local` 文件不应该提交到版本控制系统中，请将它添加到 `.gitignore`
3. 环境变量在构建时被静态替换，修改后需要重启开发服务器

## 飞书登录配置步骤

1. 前往[飞书开发者平台](https://open.feishu.cn/)创建应用
2. 获取应用的 App ID
3. 配置重定向 URI（确保与 `VITE_FEISHU_REDIRECT_URI` 一致）
4. 在应用权限中开启需要的权限（如用户信息读取等）
