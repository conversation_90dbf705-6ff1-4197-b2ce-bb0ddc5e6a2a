# 部署脚本使用说明

本项目提供了两个部署脚本，用于自动构建项目并上传到远程服务器。

## 脚本文件

1. **deploy.sh** - 基础部署脚本，通过命令行参数指定服务器信息
2. **deploy-with-config.sh** - 使用配置文件的部署脚本（推荐）
3. **deploy.config.example** - 配置文件示例

## 功能特性

- ✅ 使用 `pnpm build-without-type-check` 命令构建项目
- ✅ 自动清理旧的构建产物
- ✅ 备份远程服务器上的旧文件
- ✅ 使用 rsync 高效同步文件
- ✅ 部署失败时自动恢复备份
- ✅ 彩色日志输出，清晰显示进度
- ✅ 自动清理旧备份（保留最近3个）
- ✅ 部署前检查环境和连接

## 使用方法

### 方法一：使用配置文件（推荐）

1. **创建配置文件**
   ```bash
   cp deploy.config.example deploy.config
   ```

2. **编辑配置文件**
   ```bash
   nano deploy.config
   ```
   
   填入您的服务器信息：
   ```bash
   SERVER=your-server.com
   USER=root
   PORT=22
   ```

3. **执行部署**
   ```bash
   ./deploy-with-config.sh
   ```

### 方法二：直接使用命令行参数

```bash
./deploy.sh <服务器地址> <用户名> [端口号]
```

示例：
```bash
./deploy.sh example.com root
./deploy.sh example.com root 2222
```

## 前置要求

### 本地环境
- ✅ pnpm（用于构建项目）
- ✅ rsync（用于文件同步）
- ✅ ssh（用于连接服务器）

### 服务器配置
- ✅ SSH 密钥认证已配置（推荐）或密码认证
- ✅ 远程服务器上有 `/home/<USER>
- ✅ rsync 已安装

## 部署流程

1. **环境检查** - 检查本地工具和服务器连接
2. **清理构建** - 删除旧的 dist 目录
3. **项目构建** - 执行 `pnpm build-without-type-check`
4. **备份旧文件** - 在服务器上备份当前网站文件
5. **上传新文件** - 使用 rsync 同步 dist 目录到服务器
6. **验证部署** - 检查部署结果
7. **清理备份** - 保留最近3个备份，删除更旧的

## 安全特性

- **自动备份**：部署前自动备份旧文件
- **失败恢复**：上传失败时自动恢复备份
- **连接测试**：部署前测试服务器连接
- **权限检查**：检查必要的工具和权限

## 故障排除

### 常见问题

1. **pnpm 未安装**
   ```bash
   npm install -g pnpm
   ```

2. **SSH 连接失败**
   - 检查服务器地址和端口
   - 确保 SSH 密钥已配置
   - 测试手动 SSH 连接

3. **权限不足**
   - 确保用户对 `/home/<USER>
   - 检查 SSH 用户权限

4. **构建失败**
   - 检查项目依赖是否完整
   - 运行 `pnpm install` 安装依赖
   - 手动测试构建命令

### 日志说明

脚本使用彩色日志：
- 🔵 **[INFO]** - 信息提示
- 🟢 **[SUCCESS]** - 成功操作
- 🟡 **[WARNING]** - 警告信息
- 🔴 **[ERROR]** - 错误信息

## 配置选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| SERVER | 无 | 服务器地址（必需） |
| USER | 无 | SSH 用户名（必需） |
| PORT | 22 | SSH 端口 |
| REMOTE_PATH | /home/<USER>
| LOCAL_DIST_PATH | ./dist | 本地构建目录 |
| BUILD_COMMAND | pnpm build-without-type-check | 构建命令 |

## 示例配置

```bash
# 生产环境
SERVER=prod.example.com
USER=deploy
PORT=22

# 测试环境
SERVER=test.example.com
USER=root
PORT=2222
```

## 注意事项

1. 首次使用前请确保 SSH 密钥认证已配置
2. 建议在测试环境先验证脚本功能
3. 部署会完全替换远程目录内容，请确保备份重要数据
4. 脚本会自动保留最近3个备份，定期清理更旧的备份
